# Gyro Systems UI

A modern web application built with Vue 3 and Vite, providing a comprehensive interface for system management and monitoring.

## Features

- Multi-language support (English, Traditional Chinese, Simplified Chinese, Japanese)
- Responsive design with Bootstrap 5
- Interactive dashboards with ApexCharts
- Calendar integration with FullCalendar
- Form validation with Vuelidate
- Icon support with Feather Icons
- Animation effects with AOS

## Tech Stack

- **Framework**: Vue 3 with Composition API
- **Build Tool**: Vite
- **State Management**: Pinia
- **Routing**: Vue Router
- **UI Components**: Bootstrap Vue Next
- **HTTP Client**: Axios
- **CSS Preprocessors**: SCSS, Less
- **Code Quality**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stylelint
- **Git Hooks**: <PERSON><PERSON>, lint-staged

## Getting Started

### Prerequisites

- Node.js (v18.0.0 or higher)
- PNPM (v8.6.10 or higher)

### Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd <project-directory>

# Install dependencies
pnpm install
```

### Development

```bash
# Start the development server
pnpm dev
```

The application will be available at http://localhost:5173

### Building for Production

```bash
# Build the application
pnpm build

# Preview the production build
pnpm preview
```

## Environment Configuration

The project uses different environment configurations:

- `.env.development` - Development environment settings
- `.env.production` - Production environment settings

## Project Structure

```
├── public/             # Static assets
├── src/
│   ├── assets/         # Images, fonts, etc.
│   ├── components/     # Reusable Vue components
│   ├── locales/        # Internationalization files
│   ├── router/         # Vue Router configuration
│   ├── store/          # Pinia store modules
│   ├── views/          # Page components
│   ├── App.vue         # Root component
│   └── main.js         # Application entry point
├── index.html          # HTML template
└── vite.config.js      # Vite configuration
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
