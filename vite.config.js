import { defineConfig, loadEnv } from 'vite';
// import { resolve } from 'path';
import vue from '@vitejs/plugin-vue';
import Icons from 'unplugin-icons/vite';
import eslintPlugin from 'vite-plugin-eslint'
import IconsResolver from 'unplugin-icons/resolver';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { fileURLToPath, URL } from "url";

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, process.cwd());

  return defineConfig({
    base: VITE_BASE_URL,
    plugins: [
      vue(),
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        resolvers: [
          IconsResolver({
            prefix: 'Icon',
          }),
        ],
        eslintrc: {
          enabled: true,
        },
        dts: true,
      }),
      Components({
        resolvers: [
          IconsResolver({
            enabledCollections: ['lets-icons'],
          }),
        ],
      }),
      Icons({
        autoInstall: true,
      }),
      eslintPlugin({
        include: ['src/**/*.js', 'src/**/*.vue', 'src/*.js', 'src/*.vue']
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src',
        import.meta.url)),
      },
    },
    optimizeDeps: {
      include: ['mitt', 'dayjs', 'axios', 'pinia', '@vueuse/core'],
      exclude: ['@iconify-icons/lets-icons'],
    },
    server: {
      // 通訊埠
      port: 5173,
      // port: VITE_PORT
      // 監聽地址
      host: 'localhost',
      // 服務器啟動時自動在瀏覽器中打開應用程序
      open: false,
      // 允許跨域
      cors: true,
      // 自定義代理規則
      proxy: {},
      // 預處理中間件
      warmup: {
        clientFiles: ['./index.html', './src/{views,components}/*'],
      },
    },
    build: {
      // 設定最終構建的瀏覽器兼容目標
      target: 'es2015',
      // 生成後是否產生 source map 文件
      sourcemap: false,
      // chunk 大小超過會在控制台警告的限制（以 kbs 為單位） 
      chunkSizeWarningLimit: 5000,
      // 啟用/禁用 gzip 壓縮大小報告
      reportCompressedSize: false,
      // 自定義底層的 Rollup 打包配置
      rollupOptions: {
        output: {
          // 指定 chunks 的入口文件模式
          entryFileNames: 'static/js/[name]-[hash].js',
          // 對代碼分割中產生的 chunk 自定義命名
          chunkFileNames: 'static/js/[name]-[hash].js',
          // 自定義創建結果中的靜態資源名稱
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          // 壓縮 Rollup 輸出結果
          compact: true,
          // 創建自定義的公共 chunk
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia'],
          },
        },
      },
    },
  });
};

