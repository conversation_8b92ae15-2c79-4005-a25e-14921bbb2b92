import { getCookieToken } from '@/utils/cookies'

const checkAuth = async () => {
  if (!getCookieToken()) {
    return { path: '/login' }
  } else {
    return true
  }
}


export default [
  {
    path: "/",
    name: "Dashboard",
    component: () => import("@/views/dashboard/main/index.vue"),
    beforeEnter: [checkAuth],
    meta: {
      title: "Dashboard - Gyro Systems",
    }
  },
  {
    path: "/ipc-management",
    name: "IPC_Management",
    component: () => import("@/views/dashboard/ipc/index.vue"),
    beforeEnter: [checkAuth],
    meta: {
      title: "IPC Management - Gyro Systems",
    },
  },
  {
    path: "/server-management",
    name: "Server_Management",
    component: () => import("@/views/dashboard/server/index.vue"),
    beforeEnter: [checkAuth],
    meta: {
      title: "Server Management - Gyro Systems",
    },
  },
  {
    path: "/log-management",
    name: "Log_Management",
    component: () => import("@/views/dashboard/log/index.vue"),
    beforeEnter: [checkAuth],
    meta: {
      title: "Log Management - Gyro Systems",
    },
  },
  {
    path: "/message-management",
    name: "Message_Management",
    component: () => import("@/views/dashboard/message/index.vue"),
    beforeEnter: [checkAuth],
    meta: {
      title: "Message Management - Gyro Systems",
    },
  },
  {
    path: "/account-management",
    name: "Account_Management",
    component: () => import("@/views/dashboard/account/index.vue"),
    beforeEnter: [checkAuth],
    meta: {
      title: "Account Management - Gyro Systems",
    },
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/account/login.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/notfound.vue')
  }
];