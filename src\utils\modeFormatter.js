/**
 * 統一的 Mode 狀態格式化工具
 * 用於 Main Dashboard 和 Modal 中的 Mode 狀態顯示
 */

/**
 * Mode 值映射
 */
export const MODE_VALUES = {
  AUTO: 1,
  MANUAL: 2
};

/**
 * Mode 文字映射
 */
export const MODE_TEXT = {
  [MODE_VALUES.AUTO]: 'Auto',
  [MODE_VALUES.MANUAL]: 'Manual'
};

/**
 * Mode CSS 類別配置 - 與 Main Dashboard 保持一致
 */
export const MODE_STYLES = {
  [MODE_VALUES.AUTO]: {
    badgeClass: 'text-bg-success',
    text: MODE_TEXT[MODE_VALUES.AUTO]
  },
  [MODE_VALUES.MANUAL]: {
    badgeClass: 'text-bg-warning', 
    text: MODE_TEXT[MODE_VALUES.MANUAL]
  },
  default: {
    badgeClass: 'text-bg-danger',
    text: 'Out of Service'
  }
};

/**
 * 格式化 Mode 狀態為 HTML Badge
 * @param {number} modeValue - Mode 數值 (1=Auto, 2=Manual, other=Out of Service)
 * @param {Object} options - 格式化選項
 * @param {boolean} options.rounded - 是否使用圓角樣式 (預設: true)
 * @param {boolean} options.uppercase - 是否轉換為大寫 (預設: true)
 * @param {string} options.size - Badge 大小 (預設: '')
 * @returns {string} HTML Badge 字串
 */
export function formatModeStatus(modeValue, options = {}) {
  const {
    rounded = true,
    uppercase = true,
    size = ''
  } = options;

  // 取得對應的樣式配置
  const config = MODE_STYLES[modeValue] || MODE_STYLES.default;
  
  // 組合 CSS 類別
  const cssClasses = [
    'badge',
    config.badgeClass,
    rounded ? 'rounded-pill' : '',
    uppercase ? 'text-uppercase' : '',
    size ? `badge-${size}` : ''
  ].filter(Boolean).join(' ');

  return `<span class="${cssClasses}">${config.text}</span>`;
}

/**
 * Tabulator 格式化函數 - 用於表格欄位
 * @param {Object} cell - Tabulator cell 物件
 * @param {Object} options - 格式化選項
 * @returns {string} HTML Badge 字串
 */
export function tabulatorModeFormatter(cell, options = {}) {
  const modeValue = cell.getValue();
  return formatModeStatus(modeValue, options);
}

/**
 * Vue 組件格式化函數 - 用於 Vue 模板
 * @param {number} modeValue - Mode 數值
 * @param {Object} options - 格式化選項  
 * @returns {string} HTML Badge 字串
 */
export function vueModeFormatter(modeValue, options = {}) {
  return formatModeStatus(modeValue, options);
}

/**
 * 取得 Mode 的純文字表示
 * @param {number} modeValue - Mode 數值
 * @returns {string} Mode 文字
 */
export function getModeText(modeValue) {
  const config = MODE_STYLES[modeValue] || MODE_STYLES.default;
  return config.text;
}

/**
 * 取得 Mode 的 CSS 類別
 * @param {number} modeValue - Mode 數值
 * @returns {string} CSS 類別名稱
 */
export function getModeBadgeClass(modeValue) {
  const config = MODE_STYLES[modeValue] || MODE_STYLES.default;
  return config.badgeClass;
}

/**
 * 檢查是否為有效的 Mode 值
 * @param {number} modeValue - Mode 數值
 * @returns {boolean} 是否為有效值
 */
export function isValidMode(modeValue) {
  return Object.values(MODE_VALUES).includes(modeValue);
}