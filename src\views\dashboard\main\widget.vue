<script setup>
import { ref, watch } from "vue";
import { CountTo } from "vue3-count-to";

const props = defineProps(["ipcStatusData"])

const crmWidgets = ref(
  [
        {
          id: 1,
          label: "ALARM",
          badge: "",
          icon: "ri-alert-line text-warning",
          counter: 0,
          decimals: 0,
          suffix: "",
          prefix: "",
          color: "text-warning",
          bgcolor: "bg-warning-subtle",
        },
        {
          id: 2,
          label: "OFFLINE",
          badge: "",
          icon: "ri-cloud-off-line text-danger",
          counter: 0,
          decimals: 0,
          suffix: "",
          prefix: "",
          color: "text-danger",
          bgcolor: "bg-danger-subtle",
        },
        {
          id: 3,
          label: "DISABLE",
          badge: "",
          icon: "ri-indeterminate-circle-line text-primary",
          counter: 0,
          decimals: 0,
          suffix: "",
          prefix: "",
          color: "text-primary",
          bgcolor: "bg-primary-subtle",
        },
        {
          id: 4,
          label: "NORMAL",
          badge: "",
          icon: "ri-exchange-funds-fill text-success",
          counter: 0,
          decimals: 0,
          prefix: "",
          separator: "",
          suffix: "",
          color: "text-success",
          bgcolor: "bg-success-subtle",
        },
        {
          id: 5,
          label: "TOTAL",
          badge: "",
          icon: "ri-numbers-fill text-primary",
          counter: 0,
          decimals: 0,
          separator: "",
          suffix: "",
          prefix: "",
          color: "text-primary",
          bgcolor: "bg-secondary-subtle",
        },
      ]
)

const getIpcStatus = async (data) => {
  try {
    const normalizeStatus = (status) => {
      status = status.toLowerCase().replace(/[^a-z]/g, '');
      if (status === 'offline') return 'offline';
      return status;
    };

    const dataList = data.map(item => ({ ...item, status: normalizeStatus(item.status) }));
    console.log("IPC Count Status:", dataList);

    const statusMap = {
      alarm: 0,
      offline: 1,
      "----": 2,
      normal: 3,
      total: 4
    };

    const counters = {
      alarm: 0,
      offline: 0,
      "----": 0,
      normal: 0,
      total: 0
    };

    dataList.forEach(item => {
      const status = item.status.toLocaleLowerCase ();
      if (status in statusMap) {
        counters[status]++;
      }
    });

    crmWidgets.value.forEach(item => {
      if(counters[item.label.toLowerCase()] === undefined) {
        item.counter = counters["----"];
      } else if (item.label.toLowerCase() === "total") {
        item.counter = dataList.length;        
      } else {
        item.counter = counters[item.label.toLowerCase()];
      }
    });

  } catch (error) {
    console.log(error);
  }
};

watch(() => props.ipcStatusData, (newVal) => {
  getIpcStatus(newVal);
});

</script>

<template>
  <BCard no-body class="mb-0">
    <BCardBody class="p-0">
      <BRow class="row-cols-xxl-5 row-cols-md-3 row-cols-1 g-0">
        <BCol v-for="(item, index) of crmWidgets" :key="index">
          <div :class="`${item.bgcolor} py-4 px-3`">
            <h5 :class="`${item.color} text-uppercase fs-22`">
              {{ item.label }}
              <i :class="`${item.badge} fs-18 float-end align-middle`"></i>
            </h5>
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i :class="`${item.icon} display-6`"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h2 :class="`${item.color} mb-0`">
                  {{item.prefix}}<count-to :startVal='0' :endVal='item.counter' :duration='2500'></count-to>
                  {{item.suffix}}
                </h2>
              </div>
            </div>
          </div>
        </BCol>
      </BRow>
    </BCardBody>
  </BCard>
</template>
