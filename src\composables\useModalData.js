import { ref, computed } from 'vue';

/**
 * Modal 資料管理 Composable
 * 提供統一的資料篩選、處理和狀態管理功能
 */
export function useModalData() {
  // Modal 狀態管理
  const modalState = ref({
    type: null,
    mode: null,
    originalData: [],
    filteredData: [],
    selectedItems: [],
    isVisible: false
  });

  /**
   * 將 Loadport 資料展開為平面結構
   * @param {Array} ipcData - IPC 狀態資料
   * @returns {Array} 展開後的 loadport 資料
   */
  const flattenLoadportData = (ipcData) => {
    const flattened = [];
    
    ipcData.forEach(device => {
      if (device.loadports) {
        Object.values(device.loadports).forEach(port => {
          flattened.push({
            device_id: device.device_id,
            name: device.name,
            group: device.group,
            ip: device.ip,
            port: device.port,
            ipc_enable: device.ipc_enable,
            ftp_enable: device.ftp_enable,
            // Loadport 特定資料
            port_id: port.port_id,
            port_no: port.port_no,
            dual_port: port.dual_port,
            mode: port.mode,
            port_state: port.port_state,
            status: port.status,
            detail: port.detail
          });
        });
      }
    });
    
    return flattened;
  };

  /**
   * 根據 Modal 類型和模式篩選資料
   * @param {Array} data - 原始資料
   * @param {string} type - Modal 類型 ('reset', 'auto', 'manual')
   * @param {string} tableMode - 表格模式 ('Loadport', 'IPC')
   * @returns {Array} 篩選後的資料
   */
  const filterDataByType = (data, type, tableMode) => {
    if (!data || !Array.isArray(data)) return [];

    // 如果是 Loadport 模式，需要先展開資料
    let processedData = data;
    if (tableMode === 'Loadport') {
      processedData = flattenLoadportData(data);
    }

    switch (type) {
      case 'reset':
        // Reset Modal: 顯示全部資料
        return processedData;
      
      case 'auto':
        if (tableMode === 'Loadport') {
          // Loadport Auto: 篩選 mode 為 2 的 port (Manual 模式的項目，可以切換到 Auto)
          return processedData.filter(item => item.mode === 2);
        } else {
          // IPC Auto: 顯示全部設備資料，IPC 模式下設備沒有狀態區分
          console.log('IPC Auto mode: showing all devices for batch operation');
          return processedData;
        }
      
      case 'manual':
        if (tableMode === 'Loadport') {
          // Loadport Manual: 篩選 mode 為 1 的 port (Auto 模式的項目，可以切換到 Manual)
          return processedData.filter(item => item.mode === 1);
        } else {
          // IPC Manual: 顯示全部設備資料，IPC 模式下設備沒有狀態區分
          console.log('IPC Manual mode: showing all devices for batch operation');
          return processedData;
        }
      
      default:
        return processedData;
    }
  };

  /**
   * 準備 Modal 資料
   * @param {string} type - Modal 類型
   * @param {Array} rawData - 原始資料
   * @param {string} tableMode - 表格模式
   */
  const prepareModalData = (type, rawData, tableMode) => {
    try {
      console.log(`Preparing Modal Data - Type: ${type}, Mode: ${tableMode}`);
      console.log('Raw data received:', rawData?.length || 0, 'items');

      const filteredData = filterDataByType(rawData, type, tableMode);
      console.log('Filtered data result:', filteredData?.length || 0, 'items');

      // 檢查是否是重新打開同一個Modal
      const isReopeningModal = modalState.value.type === type &&
                               modalState.value.mode === tableMode &&
                               !modalState.value.isVisible;

      // 如果是重新打開且有資料，保留現有的選中項目
      const currentSelectedItems = isReopeningModal ? (modalState.value.selectedItems || []) : [];

      modalState.value = {
        type,
        mode: tableMode,
        originalData: rawData || [],
        filteredData,
        selectedItems: currentSelectedItems,
        isVisible: true
      };

      console.log(`Modal Data Prepared - Type: ${type}, Mode: ${tableMode}`);
      console.log('Original Data Count:', rawData?.length || 0);
      console.log('Filtered Data Count:', filteredData.length);
      console.log('Is Reopening Modal:', isReopeningModal);
      console.log('Preserved Selected Items Count:', modalState.value.selectedItems.length);

      // IPC 模式特殊處理
      if (tableMode === 'IPC') {
        console.log('IPC mode: All devices available for batch operation');
      }
    } catch (error) {
      console.error('Error preparing modal data:', error);
      console.error('Raw data that caused error:', rawData);
      // 確保即使出錯也有基本的狀態
      modalState.value = {
        type,
        mode: tableMode,
        originalData: [],
        filteredData: [],
        selectedItems: [],
        isVisible: true
      };
      throw error;
    }

    return modalState.value;
  };

  /**
   * 處理項目選擇
   * @param {Array} selectedRows - 選中的行資料
   */
  const handleItemSelection = (selectedRows) => {
    modalState.value.selectedItems = selectedRows || [];
    console.log('handleItemSelection - Selected Items Count:', modalState.value.selectedItems.length);
    if (modalState.value.selectedItems.length > 0) {
      console.log('handleItemSelection - First selected item keys:', Object.keys(modalState.value.selectedItems[0]));
      console.log('handleItemSelection - First selected item sample:', modalState.value.selectedItems[0]);
    }
  };

  /**
   * 全選/取消全選
   * @param {boolean} selectAll - 是否全選
   */
  const toggleSelectAll = (selectAll) => {
    if (selectAll) {
      modalState.value.selectedItems = [...modalState.value.filteredData];
      console.log('toggleSelectAll - Selected all items, count:', modalState.value.selectedItems.length);
      if (modalState.value.selectedItems.length > 0) {
        console.log('toggleSelectAll - First item keys:', Object.keys(modalState.value.selectedItems[0]));
      }
    } else {
      modalState.value.selectedItems = [];
      console.log('toggleSelectAll - Cleared all selections');
    }
  };

  /**
   * 重置 Modal 狀態
   */
  const resetModalState = () => {
    try {
      console.log('Resetting modal state...');
      modalState.value = {
        type: null,
        mode: null,
        originalData: [],
        filteredData: [],
        selectedItems: [],
        isVisible: false
      };
      console.log('Modal state reset successfully');
    } catch (error) {
      console.error('Error resetting modal state:', error);
      // 強制重置，即使出錯
      modalState.value = {
        type: null,
        mode: null,
        originalData: [],
        filteredData: [],
        selectedItems: [],
        isVisible: false
      };
    }
  };

  /**
   * 獲取選中項目的統計資訊
   */
  const selectionStats = computed(() => {
    const total = modalState.value.filteredData.length;
    const selected = modalState.value.selectedItems.length;
    
    return {
      total,
      selected,
      hasSelection: selected > 0,
      isAllSelected: selected === total && total > 0,
      selectionText: `${selected} / ${total} selected`
    };
  });

  /**
   * 檢查是否有可操作的資料
   */
  const hasOperableData = computed(() => {
    return modalState.value.filteredData.length > 0;
  });

  return {
    // 狀態
    modalState,
    
    // 計算屬性
    selectionStats,
    hasOperableData,
    
    // 方法
    filterDataByType,
    prepareModalData,
    handleItemSelection,
    toggleSelectAll,
    resetModalState
  };
}

/**
 * 資料驗證工具
 */
export function validateModalData(data, type) {
  if (!Array.isArray(data)) {
    console.warn(`Invalid data for ${type} modal:`, data);
    return false;
  }
  
  if (data.length === 0) {
    console.info(`No data available for ${type} modal`);
    return true; // 空資料是有效的
  }
  
  // 檢查必要欄位
  const requiredFields = ['device_id', 'name'];
  const hasRequiredFields = data.every(item => 
    requiredFields.every(field => Object.prototype.hasOwnProperty.call(item, field))
  );
  
  if (!hasRequiredFields) {
    console.warn(`Missing required fields in ${type} modal data`);
    return false;
  }
  
  return true;
}