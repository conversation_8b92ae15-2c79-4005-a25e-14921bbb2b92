import { createI18n } from "vue-i18n";


async function loadLocales() {
  const messages = {}

  const locales = [
    { locale: 'en-US', path: './locales/en/' },
    { locale: 'zh-CN', path: './locales/zh-CN/' },
    { locale: 'zh-TW', path: './locales/zh-TW/' },
    { locale: 'ja-JPN', path: './locales/ja/' }
  ]

  for (const { locale, path } of locales) {
    const module = await import(`${path}index.json`)
    messages[locale] = module.default
  }

  return messages
}

const i18n = new createI18n({
    locale: localStorage.getItem('locales') || 'zh-TW',
    messages: loadLocales()
  })
  

export default i18n;
