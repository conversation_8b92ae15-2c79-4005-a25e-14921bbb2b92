# 模式切換無反應問題調試

## 🔍 **問題現象**
- 資料堆疊問題已解決 ✅
- 但模式切換後無法切回：切到 IPC 後，無法切回 LOADPORT ❌

## 🛠️ **已實施的修復**

### **1. 改善 tableBuilt 回調**
```javascript
// 修復前
tableBuilt: function() {
  updateTableData();  // ❌ 沒有傳入資料
}

// 修復後
tableBuilt: function() {
  console.log('Tabulator built for mode:', props.mode);
  if (tableData.value && tableData.value.length > 0) {
    updateTableData(tableData.value);  // ✅ 傳入正確資料
  }
}
```

### **2. 增強模式切換錯誤處理**
```javascript
watch(() => props.mode, (newMode, oldMode) => {
  try {
    console.log('Mode change detected:', oldMode, '→', newMode);
    
    stopInterval()
    tableTabulatorFunc();  // 重新初始化
    
    // 更新追蹤狀態
    currentMode.value = newMode;
    currentIndexField.value = newMode === 'Loadport' ? 'port_id' : 'device_id';
    
    refresh(newMode)
    startInterval();
  } catch (error) {
    console.error('Error during mode change:', error);
    // 錯誤恢復
    startInterval();
  }
});
```

### **3. 改善 updateTableData 資料檢查**
```javascript
// 新增空資料檢查
if (!newData || newData.length === 0) {
  console.log('No data provided, clearing table');
  tableTabulator.value.setData([]);
  return;
}
```

## 🧪 **調試步驟**

### **步驟 1：檢查控制台日誌**
切換模式時應該看到以下日誌序列：
```
Mode change detected: Loadport → IPC
Stopped interval for mode change
Reinitializing Tabulator for mode change
Tabulator built for mode: IPC
Tabulator reinitialized successfully
Updated tracking state - mode: IPC indexField: device_id
Refresh called for new mode
Started interval for new mode
```

### **步驟 2：檢查是否有錯誤**
查看控制台是否有以下錯誤：
- `Error during mode change:`
- `Failed to restart interval:`
- `Tabulator instance not available`
- `updateTableData error:`

### **步驟 3：檢查父組件狀態**
確認 Main Dashboard 的 `tableMode` 是否正確更新：
```javascript
// 在 main/index.vue 中檢查
watch(tableMode, (newMode) => {
  console.log(`Switched to mode: ${newMode}`);  // 這個應該被觸發
});
```

## 🔧 **可能的問題和解決方案**

### **問題 1：父組件的 tableMode 沒有更新**
**檢查**：Main Dashboard 的模式切換按鈕是否正確更新 `tableMode.value`
**解決**：確認按鈕點擊事件正確設置 `tableMode.value = 'IPC'` 或 `'Loadport'`

### **問題 2：Tabulator 重新初始化失敗**
**檢查**：`tableTabulatorFunc()` 是否拋出錯誤
**解決**：添加 try-catch 包裝 `tableTabulatorFunc()`

### **問題 3：資料獲取失敗**
**檢查**：`refresh(newMode)` 和 `startInterval()` 是否正常工作
**解決**：確認 API 調用正常，資料能正確返回

### **問題 4：DOM 元素問題**
**檢查**：`tableRef.value` 是否存在
**解決**：確認 DOM 元素沒有被意外銷毀

## 📋 **調試清單**

請按順序檢查：

- [ ] **控制台日誌**：模式切換時是否有完整的日誌序列
- [ ] **錯誤信息**：是否有任何 JavaScript 錯誤
- [ ] **父組件狀態**：`tableMode` 是否正確更新
- [ ] **API 調用**：資料是否正確獲取
- [ ] **DOM 狀態**：表格容器是否存在
- [ ] **網路請求**：是否有 API 請求失敗

## 🎯 **下一步調試**

如果問題仍然存在，請：

1. **分享控制台日誌**：完整的模式切換過程日誌
2. **檢查網路標籤**：是否有 API 請求失敗
3. **檢查父組件**：Main Dashboard 的 `tableMode` 狀態變化
4. **嘗試簡化**：暫時移除 `tableTabulatorFunc()` 調用，看是否能切換

這些修復應該解決模式切換無反應的問題。關鍵是要確保每個步驟都正確執行，並且沒有錯誤中斷流程。
