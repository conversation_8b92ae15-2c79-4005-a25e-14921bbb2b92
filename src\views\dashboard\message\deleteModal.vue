<script setup>
import { ref, inject, reactive } from "vue";
import {
    useToast
} from "vue-toastification";
import { deleteMessageData } from "@/apis/e84_Api";

const props = defineProps(["dataIcon", "dataShow", "dataMultiShow"]);
const toast = useToast();

const modalIcon = ref(props.dataIcon);
const getData = reactive({
    id: "",
    sf: "",
    code: "",
});
const modalShow = ref(props.dataShow);
const multiModalShow = ref(props.dataMultiShow);

const refreshData = inject('refreshData')

const showModal = (data) => {
    // console.log("Device ID:", data);
    getData.sf = data.sf;
    getData.code = data.code;
    getData.id = data.id;
    modalShow.value = true;
};

const showMultiModal = (data) => {
    // console.log("Device ID:", data);
    getData.id = data;
    multiModalShow.value = true;
};

const resetFunc = async () => {
    console.log("Delete Message:", getData);
    try {
        const data = getData.id
        console.log("Delete Message ID:", data)

        const res = await deleteMessageData(data);
        console.log("Delete Device:", res);

        if(res.status >= 200 && res.status < 300) {
            toast.success(`Delete ${getData.code} Success` , {
                position: "bottom-right",
                timeout: 1000,
            });

            refreshData()
        }

    } catch (error) {
        console.log("ERROR", error);
        toast.error(`ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]` , {
            position: "bottom-right",
            timeout: 1000,
        });
    }

    modalShow.value = false;
};

defineExpose({ showModal, showMultiModal });

</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body text-center pt-0">
      <i :class="[modalIcon]" class="fs-c-10 text-danger-emphasis"></i>
      <div class="mt-0 pt-0">
        <h4>Are you sure to Delete <br> SF <span class="text-danger-emphasis">{{ getData.sf }}</span> Code  <span class="text-danger-emphasis">{{ getData.code }}</span>?</h4>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
      <BButton type="button" variant="danger" class="me-5" @click="resetFunc">Yes</BButton>
      <BButton type="button" variant="light" @click="modalShow = false"
        >Close
      </BButton>
    </div>
    </div>
  </BModal>
  <BModal
    v-model="multiModalShow"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body text-center pt-0">
      <i :class="[modalIcon]" class="fs-c-10 text-danger-emphasis"></i>
      <div class="mt-0 pt-0">
        <h4>Are you sure to Delete SF <span class="text-danger-emphasis">{{ getData.sf }}</span> Code  <span class="text-danger-emphasis">{{ getData.code }}</span>？</h4>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
        <BButton type="button" variant="danger" class="me-5" @click="resetFunc">Yes</BButton>
        <BButton type="button" variant="light" @click="modalShow = false"
          >Close
        </BButton>
      </div>
    </div>
  </BModal>
</template>