<script setup>
import { reactive, ref, onMounted } from "vue";
// import { useToast } from "vue-toastification";
import { TabulatorFull as Tabulator } from "tabulator-tables";

import { getAlarmList } from "@/apis/e84_Api";
import dayjs from 'dayjs'  

const formatDate = (isoString) => {
  return dayjs(isoString).format('YYYY/MM/DD HH:mm:ss')
}

const props = defineProps(["dataShow"]);
// const toast = useToast();

const modalShow = ref(props.dataShow);

// const refreshData = inject("refreshData");

// let tableData = ref([]);
let tableRef = ref(null);
const tableTabulator = ref(null);
// let selectedData = [];
const searchQuery = ref("");
const searchKeywords = ref("");

const showModal = () => {
  modalShow.value = true;
};

const urlData = JSON.parse(localStorage.getItem("serverConfig"));
const token = localStorage.getItem("jwt");
const basicUrl = "http://" + urlData.E84.ip_address;
const apiUrl = "/api/alarm/list";

const filterData = reactive({
  order_by: "desc",
  order_column: "id",
  search: "",
  id: 0,
  start_id: 0,
  end_id: 0,
  start_time: "",
  end_time: "",

  device_id: "",
  alcd: 0,
  alid: 0,
  altx: "",
  level: "",
  cause: "",
  detail: "",
  description: "",
  new: -1,
  start2_time: "",
  end2_time: ""
});

const downloadData = ref([]);
const downloadTable = ref(null);
const downloadTableRef = ref(null);

const tableTabulatorFunc = () => {
  try {
    tableTabulator.value = new Tabulator(tableRef.value, {
      placeholder: "No Data Available",
      dataLoaderLoading: `<span class="spinner-border flex-shrink-0" role="status">
      <span class="visually-hidden">Loading...</span>
    </span>`,
      ajaxURL: basicUrl + apiUrl,
      ajaxConfig: "POST",
      ajaxContentType: {
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + token,
        },
        body: function (url, config, params) {
          // console.log("CONTENT TYPE HEADER PARAMS", params)

          params.draw = 1
          params.start = (params.start - 1) * params.length;
          params.order_by = filterData.order_by;
          params.order_column = filterData.order_column;

          params.search = searchKeywords.value ? searchKeywords.value.trim() : "";
          params.id = filterData.id;
          params.start_id = filterData.start_id;
          params.end_id = filterData.end_id;
          params.start_time = filterData.start_time;
          params.end_time = filterData.end_time;

          params.device_id = filterData.device_id
          params.alcd = filterData.alcd
          params.alid = filterData.alid
          params.altx = filterData.altx
          params.level = filterData.level
          params.cause = filterData.cause
          params.detail = filterData.detail
          params.description = filterData.description
          params.new = filterData.new
          params.start2_time = filterData.start2_time
          params.end2_time = filterData.end2_time
          
          // console.log("CONTENT TYPE HEADER PARAMS END", params)
          return JSON.stringify(params);
        },
      },
      dataSendParams: {
        page: "start",
        size: "length",
      },
      dataReceiveParams: {
        last_page: "recordsTotal",
      },
      pagination: true,
      paginationMode: "remote",
      paginationCounter: "rows",
      paginationSize: 10,
      selectableRows: false,
      paginationSizeSelector: [10, 25, 50, 100],
      ajaxResponse: function (url, params, response) {
        let nowPageSize = params.length;
        let totalPage = response.recordsTotal / nowPageSize;
        console.log("API DATA RES", response);

        return {
          data: response.data,
          last_page: Math.ceil(totalPage),
          last_row: response.recordsTotal,
        };
      },
      autoResize: false,
      rowHeight: 46,
      layout: "fitColumns",
      reactiveData: true,
      height: 595,
      columns: [
        {
          title: "ID",
          field: "id",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 100,
        },
        {
          title: "Device ID",
          field: "device_id",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 120,
        },
        {
          title: "ALCD",
          field: "alcd",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 120,
        },
        {
          title: "ALID",
          field: "alid",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 100,
        },
        {
          title: "ALTX",
          field: "altx",
          headerHozAlign: "center",
          hozAlign: "left",
          vertAlign: "middle",
          width: 350,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${cell.getValue()}</span>`;
          },
        },
        {
          title: "Level",
          field: "level",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 150,
        },
        {
          title: "Created At",
          field: "created_at",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 200,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${formatDate(cell.getValue())}</span>`;
          },
        },
        {
          title: "Updated At",
          field: "updated_at",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 200,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${formatDate(cell.getValue())}</span>`;
          },
        },
        {
          title: "Cause",
          field: "cause",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 400,
        },
        {
          title: "Detail",
          field: "detail",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 400,
        },
        {
          title: "Description",
          field: "description",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 400,
        },
      ],
    });
  } catch (err) {
    console.error(err);
  }
};


const refreshTable = () => {
  searchQuery.value = "";
  // tableTabulator.value.destroy();
  // tableTabulatorFunc();
  tableTabulator.value.setData();
};

const resetQuery = () => {
  searchKeywords.value = "";
  tableTabulator.value.destroy();
  tableTabulatorFunc();
};

// const updateAlarmTableData = (newData) => {
//   tableAlarmTabulator.value.replaceData(newData);
//   tableAlarmTabulator.value.selectRow(alarmSelectedData.value);
// };

const onKeyUpSearch = event => {
  if (event.key === 'Enter' && Object.values(searchQuery).filter(value => value !== '').length > 0) {
    searchKeywords.value = searchQuery.value;
    refreshTable();    
  }
};

const getTodayData = async () => {

  let dateTime = "2024-02-22T14:57:06";
let date = new Date(dateTime);

// 获取开始时间 (00:00:00)
let startOfDay = new Date(date); // Initially set it to the given date
startOfDay.setHours(0, 0, 0, 0); // Set the time to start of the day

// 获取结束时间 (23:59:59)
let endOfDay = new Date(date); // Initially set it to the given date
endOfDay.setDate(endOfDay.getDate() + 1); // Add a day
endOfDay.setHours(0, 0, 0, 0); // Set the time to end of the day

let formatDateTime = (dateTime) => {
  let yyyy = dateTime.getFullYear();
  let mm = String(dateTime.getMonth() + 1).padStart(2, '0');
  let dd = String(dateTime.getDate()).padStart(2, '0');
  let hh = String(dateTime.getHours()).padStart(2, '0');
  let min = String(dateTime.getMinutes()).padStart(2, '0');
  return `${yyyy}-${mm}-${dd} ${hh}:${min}`;
}

    let data = {
      draw: 1,
      order_by: "desc",
      order_column: "id",
      search: "",
      id: 0,
      start_id: 0,
      end_id: 0,
      start_time: formatDateTime(startOfDay),
      end_time: formatDateTime(endOfDay),
      length: 5000,
      start: 0,
      device_id: "",
      alcd: 0,
      alid: 0,
      altx: "",
      level: "",
      cause: "",
      detail: "",
      description: "",
      new: -1,
      start2_time: "",
      end2_time: ""
    };

    // console.log("Post Data:", data)

    const res = await getAlarmList(data);
    
    // console.log("Today Data:", res.data);

    return res.data
};

const downloadTableFunc = () => {
        downloadTable.value = new Tabulator(downloadTableRef.value, {
            data: downloadData.value,
            columns: [
            {
          title: "ID",
          field: "id",
        },
        {
          title: "Device ID",
          field: "device_id",
        },
        {
          title: "ALCD",
          field: "alcd",
        },
        {
          title: "ALID",
          field: "alid",
        },
        {
          title: "ALTX",
          field: "altx",
        },
        {
          title: "Level",
          field: "level",
        },
        {
          title: "Created At",
          field: "created_at",
        },
        {
          title: "Updated At",
          field: "updated_at",
        },
        {
          title: "Cause",
          field: "cause",
        },
        {
          title: "Detail",
          field: "detail",
        },
        {
          title: "Description",
          field: "description",
        },
            ],
        });
}

const exportCsv = async () => {
  downloadData.value = await getTodayData();
  let timeStamp = `${new Date().getFullYear()}${(new Date().getMonth()+1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}_${new Date().getHours().toString().padStart(2, '0')}${new Date().getMinutes().toString().padStart(2, '0')}`;

        let fileName = 'AlarmLogs_'+ timeStamp +'.csv'

        console.log("CSV Data:", fileName, downloadData.value);
        downloadTable.value.setData(downloadData.value);

        downloadTable.value.download("csv", fileName);
};



defineExpose({ showModal, refreshTable});

onMounted(() => {
  tableTabulatorFunc();
  downloadTableFunc();
});
</script>
<template>
  <BCard no-body>
      <BCardHeader class="">
        <BRow class="g-4 align-items-center">
          <BCol sm="auto">
            <div class="d-flex align-items-center">
              <!-- <BCardTitle class="mb-0 flex-grow-1">{{ title }}</BCardTitle> -->
              <!-- <BButton type="button" variant="success" class="me-2" @click="exportExcel">
                <i class="las la-file-excel align-middle fs-4 me-1"></i>
                Export Excel
              </BButton> -->
              <BButton type="button" variant="success" class="me-5" @click="exportCsv">
                <i class="las la-file-csv align-middle fs-4 me-1"></i>
                Export Csv
              </BButton>
              <div v-if="searchKeywords">
                <span class="badge bg-info-subtle text-info badge-border fs-6">Search Keyword: {{ searchKeywords }}</span>
              </div>
            </div>
          </BCol>
          <BCol sm>
            <div class="d-flex justify-content-sm-end">
              <BButton
                type="button"
                class="me-2"
                variant="success"
                v-throttle="refreshTable"
                :delay="300"
              >
                <i class="ri-refresh-line align-middle me-1"></i>
                Refresh
              </BButton>
              <BButton
                type="button"
                class="me-2"
                variant="success"
                v-throttle="resetQuery"
                :delay="300"
              >
                Reset
              </BButton>
              <div class="search-box ms-2">
                <input
                  type="text"
                  class="form-control"
                  id="searchResultList"
                  placeholder="Search ..."
                  v-model="searchQuery"
                  @keyup="onKeyUpSearch"
                  on
                />
                <i class="ri-search-line search-icon"></i>
              </div>
            </div>
          </BCol>
        </BRow>
      </BCardHeader>
      <BCardBody>
        <div class="table-responsive table-card position-relative vl-parent">
          <div id="tableTabulator" ref="tableRef" class="mb-2"></div>
          <div class="d-none" id="downloadTabulator" ref="downloadTableRef"></div>
        </div>
      </BCardBody>
    </BCard>
</template>
<style>
.tabulator .tabulator-alert .tabulator-alert-msg.tabulator-alert-state-msg {
  border: 0px solid #000000 !important;
  background-color: rgba(0, 0, 0, 0) !important;
}
</style>