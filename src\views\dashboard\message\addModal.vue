<script setup>
import { reactive, ref, watch, inject } from "vue";
import { useToast } from "vue-toastification";
import { addMessageData } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();


const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const addData = reactive({
  sf: null,
  code: 0,
  subcode: 0,
  msgtext: "",
  description: "",
  argp: false,
  ens: false,
  eqas: false,
  webapi: false
});

const showModal = () => {
  modalShow.value = true;
};


const resetForm = () => {
      addData.sf = null
      addData.code = 0
      addData.subcode = 0
      addData.msgtext = ""
      addData.description = ""
      addData.argp = false
      addData.ens = false
      addData.eqas = false
      addData.webapi = false
    }

const addMsgFunc = async () => {
  console.log("Add Message:", addData);
  try {
    const data = {
      sf: addData.sf,
      code: addData.code,
      subcode: addData.subcode,
      msgtext: addData.msgtext,
      description: addData.description,
      argp: addData.argp,
      ens: addData.ens,
      eqas: addData.eqas,
      webapi: addData.webapi

    };
    console.log("ADD DATA:", data);

    const res = await addMessageData(data);
    console.log("Add Message:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Add Message ${addData.code} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

watch(
  () => modalShow.value,
  (newValue) => {
    if (newValue === false) {
      resetForm();
    }
  }
);

defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Add Message"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3">
        <BCol xxl="6">
          <div>
            <label for="sf" class="form-label">SF</label>
            <BFormSelect v-model="addData.sf" class="form-select mb-3"
                    aria-label="Default select">
                    <BFormSelectOption :value="null">Select SF</BFormSelectOption>
                    <BFormSelectOption value="S05F01">S05F01</BFormSelectOption>
                    <BFormSelectOption value="S06F11">S06F11</BFormSelectOption>
                  </BFormSelect>
          </div>
        </BCol>
      </BRow>
      <BRow class="g-3 mb-3">
        <BCol xxl="6">
          <div>
            <label for="code" class="form-label">Code</label>
            <input
              v-model="addData.code"
              type="number"
              class="form-control"
              id="code"
              placeholder="Enter code"
            />
          </div>
        </BCol>
        <BCol xxl="6">
          <label for="subcode" class="form-label">Sub Code</label>
          <input
            v-model="addData.subcode"
            type="number"
            class="form-control"
            id="subcode"
            placeholder="Enter Sub Code"
          />
        </BCol>
        <BCol xxl="6">
          <label for="msgtext" class="form-label">Message Text</label>
          <input
            v-model="addData.msgtext"
            type="text"
            class="form-control"
            id="msgtext"
            placeholder="Enter Message Text"
          />
        </BCol>
        <BCol xxl="6">
          <label for="description" class="form-label">Description</label>
          <input
            v-model="addData.description"
            type="text"
            class="form-control"
            id="description"
            placeholder="Enter IPC description"
          />
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="argpEnableSwitch"
              v-model="addData.argp"
              switch
              class="form-switch-md me-2"
            >
              ARGP Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="ensEnableSwitch"
              v-model="addData.ens"
              switch
              class="form-switch-md me-2"
            >
              ENS Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="eqasEnableSwitch"
              v-model="addData.eqas"
              switch
              class="form-switch-md me-2"
            >
              EQAS Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="webapiEnableSwitch"
              v-model="addData.webapi"
              switch
              class="form-switch-md me-2"
            >
              WebAPI Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="addMsgFunc" :disabled="!addData.sf || !addData.code || !addData.subcode || !addData.msgtext || !addData.description"
          >Submit</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
