.tabulator {
  overflow: hidden;
  border-width: 0;
  background-color: transparent;
}
.tabulator .tabulator-header {
  font-weight: 500;
  color: currentColor;
  border-top-width: 1px;
  border-bottom-width: 1px;
  --border-opacity: 1;
  border-color: #e2e8f0;
  border-color: rgba(226, 232, 240, var(--border-opacity));
  background-color: transparent;
}
.tabulator .tabulator-header .tabulator-headers .tabulator-col {
  background-color: transparent;
  border-right-width: 0;
}
.tabulator .tabulator-header .tabulator-headers .tabulator-col:hover {
  --bg-opacity: 1;
  background-color: #edf2f7;
  background-color: rgba(237, 242, 247, var(--bg-opacity));
}
.tabulator
  .tabulator-header
  .tabulator-headers
  .tabulator-col
  .tabulator-col-content {
  padding: 0.75rem 1.25rem;
}
.tabulator
  .tabulator-header
  .tabulator-headers
  .tabulator-col
  .tabulator-col-content
  .tabulator-col-title {
  padding-right: 0;
}
.tabulator
  .tabulator-header
  .tabulator-headers
  .tabulator-col
  .tabulator-col-content
  .tabulator-arrow {
  top: -3px;
  border-left-width: 5px;
  border-right-width: 5px;
  bottom: 0;
  margin-top: auto;
  margin-bottom: auto;
}
.tabulator
  .tabulator-header
  .tabulator-headers
  .tabulator-col.tabulator-sortable[aria-sort="none"]
  .tabulator-col-content
  .tabulator-arrow {
  border-bottom-color: #cbd5e0;
}
.tabulator .tabulator-row {
  border-bottom-width: 1px;
  --border-opacity: 1;
  border-color: #e2e8f0;
  border-color: rgba(226, 232, 240, var(--border-opacity));
}
.tabulator .tabulator-row.tabulator-row-even:hover,
.tabulator .tabulator-row:hover {
  --bg-opacity: 1;
  background-color: #e2e8f0;
  background-color: rgba(226, 232, 240, var(--bg-opacity));
}
.tabulator .tabulator-row.tabulator-row-even {
  --bg-opacity: 1;
  background-color: #edf2f7;
  background-color: rgba(237, 242, 247, var(--bg-opacity));
}
.tabulator .tabulator-row .tabulator-cell {
  border-right-width: 0;
  line-height: 2.8rem !important;
  padding: 0px !important;
}
.tabulator .tabulator-row .tabulator-cell.tabulator-row-handle {
  padding-left: 0;
  padding-right: 0;
}
.tabulator
  .tabulator-row
  .tabulator-cell
  .tabulator-responsive-collapse-toggle {
  width: 1rem;
  height: 1rem;
  margin-right: -1.25rem;
  --bg-opacity: 1;
  background-color: #a0aec0;
  background-color: rgba(160, 174, 192, var(--bg-opacity));
  border-radius: 9999px;
}
.tabulator .tabulator-row .tabulator-responsive-collapse {
  padding: 0.75rem;
  --border-opacity: 1;
  border-color: #e2e8f0;
  border-color: rgba(226, 232, 240, var(--border-opacity));
  border-bottom-width: 0;
}
.tabulator .tabulator-row .tabulator-responsive-collapse td {
  padding: 0.5rem;
}
.tabulator .tabulator-row .tabulator-responsive-collapse td strong {
  font-weight: 500;
}
.tabulator .tabulator-footer {
  background-color: transparent;
  border-top-width: 0;
  padding: 0;
  margin-top: 0.5rem;
}
@media (max-width: 767px) {
  .tabulator .tabulator-footer {
    white-space: normal;
  }
}
.tabulator .tabulator-footer .tabulator-paginator {
  display: flex;
  align-items: center;
}
@media (max-width: 767px) {
  .tabulator .tabulator-footer .tabulator-paginator {
    display: block;
    text-align: left;
  }
}
.tabulator .tabulator-footer .tabulator-paginator > label {
  font-weight: 400;
  --text-opacity: 1;
  color: #2d3748;
  color: rgba(45, 55, 72, var(--text-opacity));
}
.tabulator .tabulator-footer .tabulator-paginator .tabulator-page-size {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-size: 15px;
  background-position: center right 0.6rem;
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  margin-left: 0.5rem;
  margin-right: auto;
  border-radius: 0.375rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  --bg-opacity: 1;
  background-color: #fff;
  background-color: rgba(255, 255, 255, var(--bg-opacity));
  --border-opacity: 1;
  border-color: #e2e8f0;
  border-color: rgba(226, 232, 240, var(--border-opacity));
  background-repeat: no-repeat;
}
@media (max-width: 767px) {
  .tabulator .tabulator-footer .tabulator-paginator .tabulator-page-size {
    margin-right: 0.75rem;
  }
}
.tabulator .tabulator-footer .tabulator-paginator .tabulator-page {
  min-width: 40px;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-color: transparent;
  --text-opacity: 1;
  color: #2d3748;
  color: rgba(45, 55, 72, var(--text-opacity));
}
@media (max-width: 639px) {
  .tabulator .tabulator-footer .tabulator-paginator .tabulator-page {
    margin-right: 0;
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
}
.tabulator .tabulator-footer .tabulator-paginator .tabulator-page:hover {
  --bg-opacity: 1;
  background-color: #e2e8f0;
  background-color: rgba(226, 232, 240, var(--bg-opacity));
  color: currentColor;
}
.tabulator .tabulator-footer .tabulator-paginator .tabulator-page.active {
  font-weight: 500;
}
.tabulator .tabulator-footer .tabulator-paginator .tabulator-page.active,
.tabulator .tabulator-footer .tabulator-paginator .tabulator-page.active:hover {
  --bg-opacity: 1;
  background-color: #edf2f7;
  background-color: rgba(237, 242, 247, var(--bg-opacity));
}
.tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page[data-page="first"],
.tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page[data-page="last"],
.tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page[data-page="next"],
.tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page[data-page="prev"] {
  width: 1.25rem;
  color: transparent;
}
.tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page[data-page="first"] {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' stroke='%232d3748' stroke-width='1.1' fill='none' stroke-linecap='round' stroke-linejoin='round' class='css-i6dzq1'%3E%3Cpath d='M11 17l-5-5 5-5M18 17l-5-5 5-5'/%3E%3C/svg%3E");
  background-size: 50%;
  background-position: 50%;
  background-repeat: no-repeat;
}
.tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page[data-page="prev"] {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' stroke='%232d3748' stroke-width='1.1' fill='none' stroke-linecap='round' stroke-linejoin='round' class='css-i6dzq1'%3E%3Cpath d='M15 18l-6-6 6-6'/%3E%3C/svg%3E");
  background-size: 45%;
  background-position: 50%;
  background-repeat: no-repeat;
}
.tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page[data-page="next"] {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' stroke='%232d3748' stroke-width='1.1' fill='none' stroke-linecap='round' stroke-linejoin='round' class='css-i6dzq1'%3E%3Cpath d='M9 18l6-6-6-6'/%3E%3C/svg%3E");
  background-size: 45%;
  background-position: 50%;
  background-repeat: no-repeat;
}
.tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page[data-page="last"] {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' stroke='%232d3748' stroke-width='1.1' fill='none' stroke-linecap='round' stroke-linejoin='round' class='css-i6dzq1'%3E%3Cpath d='M13 17l5-5-5-5M6 17l5-5-5-5'/%3E%3C/svg%3E");
  background-size: 50%;
  background-position: 50%;
  background-repeat: no-repeat;
}
.tabulator .tabulator-tableHolder .tabulator-placeholder span {
  --text-opacity: 1;
  color: #718096;
  color: rgba(113, 128, 150, var(--text-opacity));
  font-weight: 400;
  font-size: 0.875rem;
}
.tabulator .tabulator-loader {
  background: hsla(0, 0%, 100%, 0.7411764705882353);
}
.tabulator .tabulator-loader .tabulator-loader-msg {
  font-weight: 400;
  font-size: 1rem;
  background-color: transparent;
}
.tabulator .tabulator-loader .tabulator-loader-msg.tabulator-loading {
  border-width: 0;
  --text-opacity: 1;
  color: #2d3748;
  color: rgba(45, 55, 72, var(--text-opacity));
}
.tabulator .tabulator-loader .tabulator-loader-msg.tabulator-error {
  border-width: 0;
  --text-opacity: 1;
  color: #d32929;
  color: rgba(211, 41, 41, var(--text-opacity));
}
.dark .tabulator .tabulator-header {
  --border-opacity: 1;
  border-color: #3f4865;
  border-color: rgba(63, 72, 101, var(--border-opacity));
}
.dark .tabulator .tabulator-header .tabulator-headers .tabulator-col:hover {
  --bg-opacity: 1;
  background-color: #232a3b;
  background-color: rgba(35, 42, 59, var(--bg-opacity));
}
.dark .tabulator .tabulator-table {
  background-color: transparent;
  --text-opacity: 1;
  color: #e2e8f0;
  color: rgba(226, 232, 240, var(--text-opacity));
}
.dark .tabulator .tabulator-table .tabulator-row {
  background-color: transparent;
  border-color: transparent;
}
.dark .tabulator .tabulator-table .tabulator-row.tabulator-row-even:hover,
.dark .tabulator .tabulator-table .tabulator-row:hover {
  --bg-opacity: 1;
  background-color: #232a3b;
  background-color: rgba(35, 42, 59, var(--bg-opacity));
}
.dark .tabulator .tabulator-table .tabulator-row.tabulator-row-even {
  --bg-opacity: 1;
  background-color: #293145;
  background-color: rgba(41, 49, 69, var(--bg-opacity));
}
.dark .tabulator .tabulator-footer .tabulator-paginator > label {
  --text-opacity: 1;
  color: #e2e8f0;
  color: rgba(226, 232, 240, var(--text-opacity));
}
.dark .tabulator .tabulator-footer .tabulator-paginator .tabulator-page-size {
  --bg-opacity: 1;
  background-color: #232a3b;
  background-color: rgba(35, 42, 59, var(--bg-opacity));
  --border-opacity: 1;
  border-color: #1e2533;
  border-color: rgba(30, 37, 51, var(--border-opacity));
  --text-opacity: 1;
  color: #e2e8f0;
  color: rgba(226, 232, 240, var(--text-opacity));
}
.dark .tabulator .tabulator-footer .tabulator-paginator .tabulator-page {
  background-color: transparent;
  --text-opacity: 1;
  color: #e2e8f0;
  color: rgba(226, 232, 240, var(--text-opacity));
}
.dark .tabulator .tabulator-footer .tabulator-paginator .tabulator-page:hover {
  --bg-opacity: 1;
  background-color: #293145;
  background-color: rgba(41, 49, 69, var(--bg-opacity));
}
.dark .tabulator .tabulator-footer .tabulator-paginator .tabulator-page.active,
.dark
  .tabulator
  .tabulator-footer
  .tabulator-paginator
  .tabulator-page.active:hover {
  --bg-opacity: 1;
  font-weight: 900;
  background-color: #232a3b;
  background-color: rgba(85, 95, 110, var(--bg-opacity));
}
.dark .tabulator .tabulator-loader {
  background: rgba(0, 0, 0, 0.23921568627450981);
}
.dark .tabulator .tabulator-loader .tabulator-loader-msg.tabulator-loading {
  --text-opacity: 1;
  color: #e2e8f0;
  color: rgba(226, 232, 240, var(--text-opacity));
}

.tabulator .tabulator-page-counter {
  min-width: 40px;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-color: transparent;
  --text-opacity: 1;
  color: #2d3748;
  color: rgba(45, 55, 72, var(--text-opacity));
  margin-right: 2rem;
  padding: 0.8rem;
  background: rgba(237, 242, 247, 0.8);
}

.dark .tabulator .tabulator-page-counter {
  background-color: transparent;
  --text-opacity: 1;
  color: #e2e8f0;
  color: rgba(226, 232, 240, var(--text-opacity));
  margin-right: 2rem;
  padding: 0.8rem;
  background: rgba(0, 0, 0, 0.23921568627450981);
}
