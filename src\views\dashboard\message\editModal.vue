<script setup>
import { reactive, ref, inject } from "vue";
import { useToast } from "vue-toastification";
import { editMessageData } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();

const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const editData = reactive({
  sf: null,
  code: 0,
  subcode: 0,
  msgtext: "",
  description: "",
  argp: false,
  ens: false,
  eqas: false,
  webapi: false,
  id: 0,
});


const showModal = (data) => {
  // console.log("Device ID:", data);
  editData.sf = data.sf;
  editData.code = data.code;
  editData.subcode = data.subcode;
  editData.msgtext = data.msgtext;
  editData.description = data.description;
  editData.argp = data.argp;
  editData.ens = data.ens;
  editData.eqas = data.eqas;
  editData.webapi = data.webapi;
  editData.id = data.id;
  
  modalShow.value = true;
};

const editMessageFunc = async () => {
  // console.log("Edit Message:", editData);
  try {
    const data = {
      sf: editData.sf,
      code: editData.code,
      subcode: editData.subcode,
      msgtext: editData.msgtext,
      description: editData.description,
      argp: editData.argp,
      ens: editData.ens,
      eqas: editData.eqas,
      webapi: editData.webapi,
      id: editData.id,
    };
    console.log("EDIT Message Post Data:", data);

    const res = await editMessageData(data);
    console.log("Edit Message Res:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Edit Message ${editData.code} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Edit Message"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3">
        <BCol xxl="6">
          <div>
            <label for="sf" class="form-label">SF</label>
            <BFormSelect v-model="editData.sf" class="form-select mb-3"
                    aria-label="Default select">
                    <BFormSelectOption :value="null">Select SF</BFormSelectOption>
                    <BFormSelectOption value="S05F01">S05F01</BFormSelectOption>
                    <BFormSelectOption value="S06F11">S06F11</BFormSelectOption>
                  </BFormSelect>
          </div>
        </BCol>
      </BRow>
      <BRow class="g-3 mb-3">
        <BCol xxl="6">
          <div>
            <label for="code" class="form-label">Code</label>
            <input
              v-model="editData.code"
              type="number"
              class="form-control"
              id="code"
              placeholder="Enter code"
            />
          </div>
        </BCol>
        <BCol xxl="6">
          <label for="subcode" class="form-label">Sub Code</label>
          <input
            v-model="editData.subcode"
            type="number"
            class="form-control"
            id="subcode"
            placeholder="Enter Sub Code"
          />
        </BCol>
        <BCol xxl="6">
          <label for="msgtext" class="form-label">Message Text</label>
          <input
            v-model="editData.msgtext"
            type="text"
            class="form-control"
            id="msgtext"
            placeholder="Enter Message Text"
          />
        </BCol>
        <BCol xxl="6">
          <label for="description" class="form-label">Description</label>
          <input
            v-model="editData.description"
            type="text"
            class="form-control"
            id="description"
            placeholder="Enter IPC description"
          />
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="argpEnableSwitch"
              v-model="editData.argp"
              switch
              class="form-switch-md me-2"
            >
              ARGP Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="ensEnableSwitch"
              v-model="editData.ens"
              switch
              class="form-switch-md me-2"
            >
              ENS Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="eqasEnableSwitch"
              v-model="editData.eqas"
              switch
              class="form-switch-md me-2"
            >
              EQAS Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="webapiEnableSwitch"
              v-model="editData.webapi"
              switch
              class="form-switch-md me-2"
            >
              WebAPI Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="editMessageFunc" :disabled="!editData.sf || !editData.code || !editData.subcode"
          >Save</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
