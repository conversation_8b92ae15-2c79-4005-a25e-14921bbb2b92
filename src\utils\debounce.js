export function debounce(fn, delay = 500) {
  
    let timer = null
    return function (...args) {
      // console.log(arguments);
      // const args = Array.from(arguments)
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }

  export function throttle(fn, delay = 300) {
  
    let timer = null
    return function (...args) {
      // console.log(arguments);
      // const args = Array.from(arguments)
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }