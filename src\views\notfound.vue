<script setup>
import { initTheme, switchTheme } from "@/utils/theme";
// 使用 Lottie 動畫
// import { ref } from 'vue';
// import animationData from "@/components/widgets/etwtznjn.json";
// import lottie from "@/components/widgets/lottie.vue";

// const defaultOptions = ref({
//   animationData: animationData,
// });

initTheme();
</script>
<template>
  <div
    class="auth-page-wrapper auth-bg-cover py-5 d-flex justify-content-center align-items-center min-vh-100"
  >
    <div class="bg-overlay"></div>
    <div class="w-auto position-absolute top-0 end-0 mt-4 me-4">
      <BButton
        type="button"
        variant="ghost-secondary"
        class="btn-icon btn-topbar rounded-circle light-dark-mode"
        @click="switchTheme()"
      >
        <i class="bx bx-moon fs-22"></i>
      </BButton>
    </div>
    <div class="auth-page-content overflow-hidden">
      <BContainer>
        <BRow class="justify-content-center">
          <BCol xl="5">
            <BCard no-body class="overflow-hidden">
              <BCardBody class="p-4">
                <div class="text-center">
                  <!-- <lottie colors="primary:#405189,secondary:#0ab39c" :options="defaultOptions" style="height:7.5rem;width:7.5rem;" /> -->
                  <img
                    src="@/assets/images/error400-cover.png"
                    alt="error img"
                    class="img-fluid error-basic-img move-animation"
                  />
                  <h1 class="text-primary mb-4">Oops !</h1>
                  <h4 class="text-uppercase">Sorry, Page not Found 😭</h4>
                  <p class="text-muted mb-4">
                    The page you are looking for not available!
                  </p>
                  <router-link to="/" class="btn btn-success"
                    ><i class="mdi mdi-home me-1"></i>Back to home</router-link
                  >
                </div>
              </BCardBody>
            </BCard>
          </BCol>
        </BRow>
      </BContainer>
    </div>
  </div>
</template>
