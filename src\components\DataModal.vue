<script setup>
import { ref, computed, watch, onBeforeUnmount, onMounted, nextTick } from "vue";
import { useToast } from "vue-toastification";
import { TabulatorFull as Tabulator } from 'tabulator-tables';
import { useModalData } from "@/composables/useModalData";
import { createModalStrategy } from "@/strategies/modalStrategies";

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ['reset', 'auto', 'manual'].includes(value)
  },
  data: {
    type: Array,
    default: () => []
  },
  tableMode: {
    type: String,
    required: true,
    validator: (value) => ['Loadport', 'IPC'].includes(value)
  }
});

const emit = defineEmits(['update:show', 'confirm', 'cancel']);

const toast = useToast();
const { 
  modalState, 
  selectionStats, 
  hasOperableData,
  prepareModalData,
  handleItemSelection,
  toggleSelectAll,
  resetModalState 
} = useModalData();

// Modal 狀態
const modalShow = ref(false);
const isLoading = ref(false);
const tabulator = ref(null);
const tableContainer = ref(null);

// 策略實例
const strategy = computed(() => {
  try {
    return createModalStrategy(props.type, props.tableMode);
  } catch (error) {
    console.error('Failed to create modal strategy:', error);
    return null;
  }
});

// Modal 配置
const modalConfig = computed(() => {
  return strategy.value?.getConfig() || {};
});

// 表格配置
const tableConfig = computed(() => {
  return strategy.value?.getTableConfig() || {};
});

// 操作按鈕配置
const actionButtons = computed(() => {
  return strategy.value?.getActionButtons() || [];
});

// 監聽 props.show 變化
watch(() => props.show, (newVal) => {
  console.log(`DataModal (${props.type}) show changed:`, newVal);
  modalShow.value = newVal;
  
  if (newVal) {
    try {
      initializeModal();
    } catch (error) {
      console.error('Error initializing modal:', error);
    }
  } else {
    // 使用 nextTick 確保 cleanup 函數已定義
    nextTick(() => {
      if (typeof cleanup === 'function') {
        cleanup();
      }
    });
  }
});

// 初始化時處理 show 狀態
onMounted(() => {
  if (props.show) {
    try {
      initializeModal();
    } catch (error) {
      console.error('Error initializing modal on mount:', error);
    }
  }
});

// 監聽資料變化 - 智能比較和更新
let updateTimeout = null;
let lastDataHash = null;
let isUserInteracting = false;
let interactionTimeout = null;

// 計算資料雜湊值用於比較
const calculateDataHash = (data) => {
  if (!data || !Array.isArray(data)) return null;
  return JSON.stringify(data.map(item => {
    if (props.tableMode === 'Loadport' && item.loadports) {
      // 對於 Loadport 模式，只關注相關欄位
      return {
        device_id: item.device_id,
        loadports: Object.values(item.loadports).map(port => ({
          port_id: port.port_id,
          mode: port.mode,
          port_state: port.port_state,
          status: port.status
        }))
      };
    } else {
      // 對於 IPC 模式，關注設備狀態
      return {
        device_id: item.device_id,
        mode: item.mode,
        ipc_enable: item.ipc_enable,
        ftp_enable: item.ftp_enable
      };
    }
  }));
};

// 標記用戶交互狀態
const markUserInteraction = () => {
  isUserInteracting = true;
  
  // 清除之前的交互計時器
  if (interactionTimeout) {
    clearTimeout(interactionTimeout);
  }
  
  // 2秒後重置交互狀態
  interactionTimeout = setTimeout(() => {
    isUserInteracting = false;
  }, 2000);
};

watch(() => props.data, (newData) => {
  if (!modalShow.value || !newData) return;
  
  // 計算新資料的雜湊值
  const newDataHash = calculateDataHash(newData);
  
  // 如果資料沒有實際變化，跳過更新
  if (newDataHash === lastDataHash) {
    return;
  }
  
  // 如果用戶正在交互，延遲更新
  if (isUserInteracting) {
    // 清除之前的更新計時器
    if (updateTimeout) {
      clearTimeout(updateTimeout);
    }
    
    // 延長等待時間，直到用戶停止交互
    updateTimeout = setTimeout(() => {
      // 再次檢查是否還在交互
      if (!isUserInteracting) {
        performDataUpdate(newData, newDataHash);
      }
    }, 1000);
    return;
  }
  
  // 清除之前的更新計時器
  if (updateTimeout) {
    clearTimeout(updateTimeout);
  }
  
  // 正常更新
  updateTimeout = setTimeout(() => {
    performDataUpdate(newData, newDataHash);
  }, 150);
}, { deep: true });

// 執行資料更新的統一函數
const performDataUpdate = (newData, newDataHash) => {
  // 保存當前選取狀態
  const selectedRows = tabulator.value ? tabulator.value.getSelectedRows().map(row => row.getData()) : [];
  
  // 更新資料
  prepareModalData(props.type, newData, props.tableMode);
  updateTable();
  
  // 更新雜湊值
  lastDataHash = newDataHash;
  
  // 恢復選取狀態
  if (selectedRows.length > 0 && tabulator.value) {
    setTimeout(() => {
      restoreSelection(selectedRows);
    }, 100);
  }
};

// 初始化 Modal
const initializeModal = () => {
  if (!strategy.value) {
    console.error('No strategy available for modal type:', props.type);
    return;
  }

  console.log(`Initializing Modal - Type: ${props.type}, Mode: ${props.tableMode}`);
  console.log('Props data available:', props.data?.length || 0, 'items');

  // 重置雜湊值
  lastDataHash = null;

  // 檢查資料是否可用
  if (!props.data || props.data.length === 0) {
    console.warn('No data available for modal initialization');
    // 仍然嘗試準備Modal資料，可能是資料還在載入中
  }

  prepareModalData(props.type, props.data, props.tableMode);

  // 計算初始資料雜湊值
  lastDataHash = calculateDataHash(props.data);

  // 延遲初始化表格，確保 DOM 已渲染
  setTimeout(() => {
    initializeTable();
  }, 100);
};

// 初始化表格
const initializeTable = () => {
  if (!tableContainer.value || !modalConfig.value.showTable) {
    console.log('Table container not ready or table not needed');
    return;
  }

  console.log('Initializing table with data:', modalState.value.filteredData?.length || 0, 'items');

  // 準備表格配置
  const config = {
    data: modalState.value.filteredData || [],
    columns: tableConfig.value.columns,
    layout: "fitColumns",
    height: tableConfig.value.height,
    pagination: tableConfig.value.pagination,
    paginationSize: tableConfig.value.pageSize,
    paginationSizeSelector: [5, 10, 20, 50],
    movableColumns: true,
    resizableRows: false,
    tooltipsHeader: true,
    printAsHtml: true,
    printHeader: "<h1>Modal Data Export</h1>",
    printFooter: "",
    downloadDataFormatter: (data) => data,
    downloadEncoder: (fileContents, blob) => blob,
  };

  try {
    // 銷毀現有表格
    if (tabulator.value) {
      console.log('Destroying existing tabulator instance');
      tabulator.value.destroy();
      tabulator.value = null;
    }

    // 檢查是否有資料可以顯示
    if (!modalState.value.filteredData || modalState.value.filteredData.length === 0) {
      console.warn('No filtered data available for table initialization');
      // 仍然創建表格，顯示空狀態
    }

    // 如果支援選擇功能
    if (tableConfig.value.selectable) {
      config.selectableRows = true;
      config.selectableRowsCheck = tableConfig.value.selectableCheck;
      config.selectableRowsRangeMode = "click";

      // 選擇事件處理
      config.rowSelectionChanged = (data) => {
        // 標記用戶交互
        markUserInteraction();

        console.log('rowSelectionChanged - received data:', data);
        console.log('rowSelectionChanged - data type:', typeof data);
        console.log('rowSelectionChanged - data length:', data ? data.length : 'undefined');

        // 獲取實際選中的行數據
        const selectedRows = tabulator.value ? tabulator.value.getSelectedRows().map(row => {
          const rowData = row.getData();
          console.log('Row data:', rowData);
          return rowData;
        }) : [];

        console.log('rowSelectionChanged - selectedRows:', selectedRows);

        // 驗證數據完整性
        if (selectedRows.length > 0) {
          const firstRow = selectedRows[0];
          const hasPortId = 'port_id' in firstRow;
          const hasDeviceId = 'device_id' in firstRow;
          console.log('Data validation - hasPortId:', hasPortId, 'hasDeviceId:', hasDeviceId);
          console.log('First row keys:', Object.keys(firstRow));
        }

        handleItemSelection(selectedRows);

        // 同步checkbox狀態
        setTimeout(() => {
          updateCheckboxStates();
        }, 10);
      };

      // 行點擊處理 - 只有在不是點擊checkbox時才切換選取
      config.rowClick = (e, row) => {
        // 標記用戶交互
        markUserInteraction();

        // 如果點擊的是checkbox，不處理行點擊
        if (e.target.classList.contains('row-selector')) {
          return;
        }

        // 切換行選取狀態
        if (row.isSelected()) {
          row.deselect();
        } else {
          row.select();
        }
      };

      // 滾動事件處理
      config.scrollVertical = () => {
        // 標記用戶交互（滾動時）
        markUserInteraction();
      };
    }

    console.log('Creating Tabulator instance with config:', {
      dataCount: config.data?.length || 0,
      columnsCount: config.columns?.length || 0,
      selectable: config.selectableRows
    });

    tabulator.value = new Tabulator(tableContainer.value, config);

    // 等待表格完全初始化
    tabulator.value.on("tableBuilt", () => {
      console.log('Table built successfully with', modalState.value.filteredData?.length || 0, 'rows');

      // 如果有資料但表格為空，嘗試重新設置資料
      if (modalState.value.filteredData?.length > 0 && tabulator.value.getDataCount() === 0) {
        console.log('Table appears empty despite having data, attempting to reload...');
        setTimeout(() => {
          if (tabulator.value) {
            tabulator.value.setData(modalState.value.filteredData);
          }
        }, 100);
      }
    });

    console.log('Table initialized successfully');
  } catch (error) {
    console.error('Failed to initialize table:', error);
    console.error('Config that caused error:', config);
    toast.error('Failed to initialize table');
  }
};

// 更新表格資料
const updateTable = () => {
  if (!tabulator.value) {
    console.log('Tabulator instance not available for update');
    return;
  }

  const dataToUpdate = modalState.value.filteredData || [];
  console.log('Updating table with', dataToUpdate.length, 'items');

  try {
    tabulator.value.setData(dataToUpdate);

    // 驗證資料是否正確設置
    setTimeout(() => {
      const currentRowCount = tabulator.value.getDataCount();
      console.log('Table updated - current row count:', currentRowCount);

      if (currentRowCount !== dataToUpdate.length) {
        console.warn(`Data count mismatch: expected ${dataToUpdate.length}, got ${currentRowCount}`);
      }

      // 更新checkbox狀態
      updateCheckboxStates();
    }, 50);
  } catch (error) {
    console.error('Error updating table data:', error);
  }
};

// 恢復選取狀態
const restoreSelection = (selectedRowsData) => {
  if (!tabulator.value || !selectedRowsData.length) return;
  
  try {
    // 根據唯一標識符恢復選取
    selectedRowsData.forEach(rowData => {
      const rows = tabulator.value.getRows();
      const matchingRow = rows.find(row => {
        const data = row.getData();
        // 使用多個欄位組合作為唯一標識
        if (props.tableMode === 'Loadport') {
          return data.device_id === rowData.device_id && 
                 data.port_id === rowData.port_id;
        } else {
          return data.device_id === rowData.device_id;
        }
      });
      
      if (matchingRow) {
        matchingRow.select();
      }
    });
    
    // 更新checkbox狀態
    setTimeout(() => {
      updateCheckboxStates();
    }, 50);
  } catch (error) {
    console.error('Error restoring selection:', error);
  }
};

// 更新checkbox狀態同步
const updateCheckboxStates = () => {
  if (!tabulator.value) return;
  
  try {
    const selectedRows = tabulator.value.getSelectedRows();
    const selectedIds = new Set(selectedRows.map(row => {
      const data = row.getData();
      // 使用與 extractAndFormatIds 相同的 ID 字段邏輯
      return props.tableMode === 'Loadport' ? data.port_id : data.device_id;
    }));
    
    console.log('updateCheckboxStates - selectedIds:', Array.from(selectedIds));
    
    // 更新所有checkbox狀態
    const rows = tabulator.value.getRows();
    rows.forEach(row => {
      const data = row.getData();
      // 使用與 extractAndFormatIds 相同的 ID 字段邏輯
      const rowId = props.tableMode === 'Loadport' ? data.port_id : data.device_id;
      
      const checkbox = row.getElement().querySelector('.row-selector');
      if (checkbox) {
        const shouldBeChecked = selectedIds.has(rowId);
        checkbox.checked = shouldBeChecked;
        console.log(`Row ${rowId}: checkbox.checked = ${checkbox.checked}, shouldBeChecked = ${shouldBeChecked}`);
      }
    });
  } catch (error) {
    console.error('Error updating checkbox states:', error);
  }
};

// 處理按鈕點擊
const handleButtonClick = async (button) => {
  console.log('handleButtonClick - button clicked:', button);
  console.log('handleButtonClick - button.action:', button.action);
  
  if (button.action === 'cancel') {
    console.log('handleButtonClick - Cancel button clicked');
    closeModal();
    return;
  }
  
  if (button.action === 'selectAll') {
    console.log('handleButtonClick - SelectAll button clicked');
    handleSelectAll();
    return;
  }
  
  if (button.action === 'confirm') {
    console.log('handleButtonClick - Confirm button clicked');
    await handleConfirm();
    return;
  }
  
  console.log('handleButtonClick - Unknown button action:', button.action);
};

// 處理全選
const handleSelectAll = () => {
  if (!tabulator.value) return;
  
  // 標記用戶交互
  markUserInteraction();
  
  const isAllSelected = selectionStats.value.isAllSelected;
  
  console.log('handleSelectAll - isAllSelected:', isAllSelected);
  console.log('handleSelectAll - Current selectedItems count:', modalState.value.selectedItems.length);
  
  if (isAllSelected) {
    console.log('handleSelectAll - Deselecting all rows');
    tabulator.value.deselectRow();
    // 手動清空選擇，確保同步
    toggleSelectAll(false);
  } else {
    console.log('handleSelectAll - Selecting all rows');
    // 使用 "all" 參數選中所有行
    tabulator.value.selectRow("all");
    // 手動設置全選，確保同步
    toggleSelectAll(true);
  }
  
  console.log('handleSelectAll - After operation selectedItems count:', modalState.value.selectedItems.length);
  
  // 同步checkbox狀態
  setTimeout(() => {
    updateCheckboxStates();
  }, 50);
};

// 處理確認操作
const handleConfirm = async () => {
  console.log('handleConfirm - Function called');
  console.log('handleConfirm - strategy.value:', strategy.value);
  console.log('handleConfirm - modalState.value.selectedItems:', modalState.value.selectedItems);
  console.log('handleConfirm - modalState.value.selectedItems.length:', modalState.value.selectedItems.length);
  
  if (!strategy.value) {
    console.error('handleConfirm - No strategy available');
    return;
  }
  
  // 驗證操作
  const validation = strategy.value.validateOperation(modalState.value.selectedItems);
  console.log('handleConfirm - validation result:', validation);
  if (!validation.isValid) {
    console.log('handleConfirm - Validation failed:', validation.message);
    toast.warning(validation.message);
    return;
  }
  
  // requireSelection 檢查已移至父組件的操作處理函數中
  
  isLoading.value = true;
  
  try {
    // 發送確認事件給父組件
    console.log('DataModal - Before emit, modalState.selectedItems:', modalState.value.selectedItems);
    console.log('DataModal - Before emit, selectedItems length:', modalState.value.selectedItems.length);
    
    // 檢查 selectedItems 是否為空
    if (modalState.value.selectedItems.length === 0) {
      console.error('DataModal - selectedItems is empty before emit!');
      console.log('DataModal - Current tabulator selected rows:', tabulator.value ? tabulator.value.getSelectedRows().length : 'no tabulator');
      
      // 嘗試重新獲取選中的行
      if (tabulator.value) {
        const currentSelected = tabulator.value.getSelectedRows().map(row => row.getData());
        console.log('DataModal - Re-fetched selected rows:', currentSelected);
        if (currentSelected.length > 0) {
          modalState.value.selectedItems = currentSelected;
          console.log('DataModal - Updated selectedItems from tabulator:', modalState.value.selectedItems.length);
        }
      }
    }
    
    // 深拷貝 selectedItems 避免引用問題
    const eventData = {
      type: props.type,
      selectedItems: JSON.parse(JSON.stringify(modalState.value.selectedItems)),
      allData: modalState.value.filteredData,
      tableMode: props.tableMode
    };
    
    console.log('DataModal - emitting confirm event with selectedItems:', eventData.selectedItems.length);
    console.log('DataModal - eventData:', eventData);
    emit('confirm', eventData);
    
    // 增加延遲時間，確保父組件有足夠時間處理 selectedItems
    setTimeout(() => {
      // 顯示成功訊息
      const successMessage = getSuccessMessage();
      toast.success(successMessage, {
        position: "bottom-right",
        timeout: 2000,
      });
      
      // 關閉 Modal
      closeModal();
    }, 500);
    
  } catch (error) {
    console.error('Operation failed:', error);
    toast.error(`Operation failed: ${error.message}`);
  } finally {
    isLoading.value = false;
  }
};

// 獲取成功訊息
const getSuccessMessage = () => {
  const count = modalState.value.selectedItems.length;
  
  switch (props.type) {
    case 'reset':
      return 'All ports have been reset successfully';
    case 'auto':
      return `Auto mode enabled for ${count} device(s)`;
    case 'manual':
      return `Manual mode enabled for ${count} device(s)`;
    default:
      return 'Operation completed successfully';
  }
};

// 關閉 Modal
const closeModal = () => {
  try {
    console.log(`DataModal (${props.type}) closing`);
    
    // 確保狀態正確重置
    modalShow.value = false;
    
    // 發送事件給父組件
    emit('update:show', false);
    emit('cancel');
    
    // 立即清理資源，避免狀態殘留
    cleanup(true);
  } catch (error) {
    console.error('Error closing modal:', error);
    // 即使出錯也要確保 Modal 關閉
    modalShow.value = false;
    emit('update:show', false);
  }
};

// 清理資源
const cleanup = (immediate = false) => {
  try {
    console.log(`DataModal cleanup started - immediate: ${immediate}, type: ${props.type}`);

    // 清理計時器
    if (updateTimeout) {
      clearTimeout(updateTimeout);
      updateTimeout = null;
    }

    if (interactionTimeout) {
      clearTimeout(interactionTimeout);
      interactionTimeout = null;
    }

    // 重置狀態
    lastDataHash = null;
    isUserInteracting = false;

    // 安全地銷毀 tabulator
    if (tabulator.value) {
      try {
        tabulator.value.destroy();
      } catch (tabulatorError) {
        console.warn('Error destroying tabulator:', tabulatorError);
      } finally {
        tabulator.value = null;
      }
    }

    // 修改清理策略：只清理選中項目，保留資料狀態
    if (immediate) {
      // 組件卸載時才完全重置狀態
      resetModalState();
    } else {
      // Modal關閉時只清理選中項目和可見狀態，保留資料
      if (modalState.value) {
        modalState.value.selectedItems = [];
        modalState.value.isVisible = false;
        console.log('Modal state partially cleaned - data preserved');
      }
    }

    console.log(`DataModal cleanup completed - type: ${props.type}`);
  } catch (error) {
    console.error('Error during cleanup:', error);
    // 確保即使清理失敗也要重置基本狀態
    try {
      if (immediate) {
        resetModalState();
      }
    } catch (resetError) {
      console.error('Critical error: Failed to reset modal state:', resetError);
    }
  }
};

// 組件卸載時清理
onBeforeUnmount(() => {
  cleanup(true); // 立即清理
});

// 計算按鈕狀態
const getButtonState = (button) => {
  if (button.requireSelection && !selectionStats.value.hasSelection) {
    return { disabled: true };
  }
  
  if (button.loading && isLoading.value) {
    return { disabled: true, loading: true };
  }
  
  return { disabled: false, loading: false };
};

// 計算全選按鈕文字
const selectAllButtonText = computed(() => {
  return selectionStats.value.isAllSelected ? 'Deselect All' : 'Select All';
});

defineExpose({ 
  modalShow,
  tabulator,
  modalState,
  selectionStats
});
</script>

<template>
  <BModal
    :model-value="modalShow"
    @update:model-value="(value) => { if (!value) closeModal(); }"
    :size="modalConfig.size || 'lg'"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
    :title="modalConfig.title"
  >
    <div class="modal-body pt-0">
      <!-- Modal Header with Icon and Description removed to save space -->
      
      <!-- Selection Stats -->
      <div class="d-flex justify-content-between align-items-center mb-3" 
           v-if="modalConfig.showStats && hasOperableData">
        <div class="text-muted">
          <small>{{ selectionStats.selectionText }}</small>
        </div>
        <div class="text-muted" v-if="modalState.filteredData.length > 0">
          <small>Total: {{ modalState.filteredData.length }} items</small>
        </div>
      </div>
      
      <!-- Data Table -->
      <div v-if="modalConfig.showTable && hasOperableData" class="mb-4">
        <div ref="tableContainer" class="table-responsive"></div>
      </div>
      
      <!-- No Data Message -->
      <div v-else-if="modalConfig.showTable && !hasOperableData" 
           class="text-center py-4">
        <div class="text-muted">
          <i class="ri-inbox-line fs-1 mb-3 d-block"></i>
          <h6>No Data Available</h6>
          <p class="mb-0">No items match the current criteria.</p>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="modal-footer v-modal-footer justify-content-center border-0 pt-0">
        <template v-for="button in actionButtons" :key="button.action">
          <BButton 
            :type="button.type || 'button'"
            :variant="button.variant"
            :class="button.action === 'cancel' ? '' : 'me-2'"
            @click="handleButtonClick(button)"
            :disabled="getButtonState(button).disabled || isLoading"
          >
            <!-- Loading Spinner -->
            <span v-if="getButtonState(button).loading" 
                  class="spinner-border spinner-border-sm me-2" 
                  role="status"></span>
            
            <!-- Button Icon -->
            <i v-if="button.icon && !getButtonState(button).loading" 
               :class="[button.icon, 'align-middle', 'me-1']"></i>
            
            <!-- Button Text -->
            <span v-if="button.action === 'selectAll'">
              {{ selectAllButtonText }}
            </span>
            <span v-else-if="getButtonState(button).loading && button.loading">
              {{ button.loadingText || 'Processing...' }}
            </span>
            <span v-else>
              {{ button.text }}
            </span>
          </BButton>
        </template>
      </div>
    </div>
  </BModal>
</template>

<style scoped>
.v-modal-custom .modal-body {
  max-height: 80vh;
  overflow-y: auto;
}

.table-responsive {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

:deep(.tabulator) {
  border: none;
  font-size: 0.875rem;
}

:deep(.tabulator .tabulator-header) {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

:deep(.tabulator .tabulator-row.tabulator-selected) {
  background-color: #e3f2fd !important;
}

:deep(.tabulator .tabulator-row:hover) {
  background-color: #f5f5f5;
}

.fs-c-10 {
  font-size: 4rem;
}

.v-modal-footer {
  padding-top: 1rem;
  margin-top: 1rem;
  border-top: 1px solid #dee2e6;
}
</style>