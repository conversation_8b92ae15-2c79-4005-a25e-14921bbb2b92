<script setup>
import { ref, computed, onMounted } from "vue";
import { useVuelidate } from "@vuelidate/core";
import { required, minLength, helpers } from "@vuelidate/validators";
import { useRouter } from "vue-router";
import { initTheme, switchTheme } from "@/utils/theme";
import { useToast } from "vue-toastification";
import { setCookieToken } from "@/utils/cookies";
import { authLogin } from "@/apis/e84_Api";

// import {
//   authMethods,
//   authFackMethods,
//   notificationMethods,
// } from "@/state/helpers";

// import { Swiper, SwiperSlide } from "swiper/vue";
// import { Autoplay, Navigation, Pagination } from "swiper/modules";

import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/navigation";
import "swiper/css/pagination";

const togglePassword = ref(false);
const processing = ref(false);
const submitted = ref(false);
const authError = ref(false);
// const tryingToLogin = ref(false);
// const isAuthError = ref(false);

const router = useRouter();
const toast = useToast();

initTheme();

const state = ref({
  username: "",
  password: "",
  rememberMe: false
});

const rules = computed(() => {
  return {
    username: {
      required: helpers.withMessage("Username is required", required),
      minLength: minLength(4),
    },
    password: {
      required: helpers.withMessage("Password is required", required),
      minLength: minLength(4),
    },
  };
});

const v$ = useVuelidate(rules, state);

const checkRemember = () => {
  const checkData = localStorage.getItem("remember_me")
    ? JSON.parse(localStorage.getItem("remember_me"))
    : null;

  if (checkData) {
    state.value.username = checkData.username;
    state.value.rememberMe = checkData.rememberMe;
  } else {
    state.value.username = "";
    state.value.rememberMe = false;
  }
};

const userNameErrorMsg = computed(() => {
  let errors = "";
  v$.value.username.$errors.forEach((error) => {
    errors.push(error.$message);
  });

  return errors;
});

const passwordErrorMsg = computed(() => {
  let errors = "";
  // console.log(v$.value);
  v$.value.password.$errors.forEach((error) => {
    errors.push(error.$message);
  });

  return errors;
});

const signinapi = async () => {
    processing.value = true;
    const userData =  {
        userid: state.value.username,
        password: state.value.password
    }
    console.log("LOGIN USER", userData);
    try {
        const result = await authLogin(userData)
        console.log("LOGIN RESULT", result);
        if (result.data.status === 'errors') {
            authError.value = result.data;
            return;
        }
        if (state.value.rememberMe == true) {
          let rememberData = {
            username: state.value.username,
            rememberMe: state.value.rememberMe,
          };
          localStorage.setItem("remember_me", JSON.stringify(rememberData));
        } else {
          localStorage.removeItem("remember_me");
        }
        localStorage.setItem('jwt', result.data.access_token);
        setCookieToken(result.data.access_token, 1)
        toast.success(`Login ${result.data.status}, Welcome ${result.data.name}!`, {
          position: "bottom-right",
          timeout: 1000,
        });
        setTimeout(() => {
            router.push('/');
        }, 1000);
    } catch (error) {
        // console.error("An error occured while trying to sign in: ", error);
        console.log("LOGIN ERROR", error);
        toast.error(`Login Error. ${error.message}`, {
          position: "bottom-right",
          timeout: 2000,
        });
    } finally {
        processing.value = false;
    }
}

const tryToLogin = () => {
  processing.value = true;
  submitted.value = true;

  v$.value.$touch();

  if (v$.value.$invalid) {
    processing.value = false;
    return;
  }
};

onMounted(() => {
  checkRemember();
});

</script>

<template>
  <div
    class="auth-page-wrapper auth-bg-cover d-flex justify-content-center align-items-center min-vh-100"
  >
    <div class="auth-page-content overflow-hidden">
      <BCard no-body class="overflow-hidden mb-0 rounded-0">
        <BRow class="g-0">
          <BCol lg="6">
            <div class="p-lg-5 p-4 auth-one-bg min-vh-100">
              <div class="bg-overlay"></div>
              <div class="position-relative h-100 d-flex flex-column">
                <div class="mb-4">
                  <router-link to="/" class="d-block">
                    <img
                      src="@/assets/images/logo/sys-logo-dark.svg"
                      alt=""
                      height="46"
                    />
                  </router-link>
                </div>
              </div>
              <footer class="footer">
                <BContainer>
                  <BRow>
                    <BCol lg="12">
                      <div class="text-center">
                        <p class="mb-0 text-white">
                          &copy; {{ new Date().getFullYear() }} Gyro Systems.
                          All Rights Reserved.
                        </p>
                      </div>
                    </BCol>
                  </BRow>
                </BContainer>
              </footer>
            </div>
          </BCol>

          <BCol lg="6">
            <div
              class="row align-items-center justify-content-center p-lg-5 p-4 min-vh-100"
            >
            <div class="w-auto position-absolute top-0 end-0 mt-4 me-4">
                <BButton type="button" variant="ghost-secondary" class="btn btn-md btn-ghost-secondary btn-icon btn-topbar rounded-circle light-dark-mode"
                @click="switchTheme()">
                <i class="bx bx-moon fs-22"></i>
                </BButton>
            </div>
              <div class="col-lg-7">
                <div>
                  <h2>Welcome!</h2>
                  <p class="text-muted">Sign in to continue to <br /></p>
                  <h1>GYRO-700 Management System</h1>
                </div>

                <div class="mt-5">
                  <form @submit.prevent="tryToLogin()">
                    <div class="mb-3">
                      <label for="username" class="form-label fs-5">Username</label>
                      <input
                        type="text"
                        class="form-control"
                        id="username"
                        placeholder="Enter username"
                        v-model="state.username"
                      />
                      <span v-if="userNameErrorMsg"> {{ userNameErrorMsg }}</span>
                    </div>
                    <div class="mb-3">
                      <label class="form-label fs-5" for="password-input"
                        >Password</label
                      >
                      <div class="position-relative auth-pass-inputgroup mb-3">
                        <input
                          :type="togglePassword ? 'text' : 'password'"
                          class="form-control pe-5"
                          placeholder="Enter password"
                          id="password-input"
                          autocomplete="on"
                          v-model="state.password"
                        />
                        <BButton
                          variant="link"
                          class="position-absolute end-0 top-0 text-decoration-none text-muted"
                          type="button"
                          id="password-addon"
                          @click="togglePassword = !togglePassword"
                          ><i class="ri-eye-fill align-middle"></i
                        ></BButton>
                      </div>
                      <span v-if="passwordErrorMsg"> {{ passwordErrorMsg }}</span>
                    </div>

                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        value=""
                        id="auth-remember-check"
                        v-model="state.rememberMe"
                      />
                      <label class="form-check-label" for="auth-remember-check"
                        >Remember me</label
                      >
                    </div>

                    <div class="mt-4">
                        <div class="mt-4">
                        <BButton variant="success" class="w-100" type="submit" @click="signinapi" :disabled="processing">
                            {{ processing ? "Please wait" : "Sign In" }}
                        </BButton>
                        </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </BCol>
        </BRow>
      </BCard>
    </div>
  </div>
</template>
