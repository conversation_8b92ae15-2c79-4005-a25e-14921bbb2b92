<script setup>
import { reactive, ref, inject } from "vue";
import { useToast } from "vue-toastification";
import { editServerData } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();

const deviceId = ref(null);
const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const editData = reactive({
  name: "",
  ip: "",
  port: "",
  url: "",
  status: "",
  svr_enable: true,
});



const showModal = (data) => {
  // console.log("Data:", data);
  editData.name = data.name;
  editData.svr_enable = data.svr_enable;
  editData.ip = data.ip;
  editData.port = data.port;
  editData.url = data.url;
  editData.status = data.status;

  modalShow.value = true;
};

const editServerFunc = async () => {
  console.log("Edit Device:", deviceId.value);
  try {
    const data = {
      name: editData.name,
      url: editData.url,
      ip: editData.ip,
      port: editData.port,
      svr_enable: editData.svr_enable,
    };
    console.log("EDIT DATA:", data);

    const res = await editServerData(data);
    console.log("Edit Server:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Edit Server ${editData.name} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
    if (res.status >= 400) {
      toast.error(`Edit Server ${editData.name} Error`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Edit Server"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3 mb-3">
        <BCol xxl="12">
          <div>
            <label for="name" class="form-label">Name</label>
            <input
              v-model="editData.name"
              type="text"
              class="form-control"
              id="name"
              placeholder="Enter name"
              disabled
            />
          </div>
        </BCol>
        <BCol xxl="4">
          <label for="ip" class="form-label">Hostname / IP</label>
          <input
            v-model="editData.ip"
            type="text"
            class="form-control"
            id="ip"
          />
        </BCol>
        <BCol xxl="2">
          <label for="port" class="form-label">Port</label>
          <input
            v-model="editData.port"
            type="text"
            class="form-control"
            id="port"
          />
        </BCol>
        <BCol xxl="6">
          <label for="url" class="form-label">Url</label>
          <input
            v-model="editData.url"
            type="text"
            class="form-control"
            id="url"
          />
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="3">
          <BFormGroup class="form-switch form-switch-right">
            <BFormCheckbox
              id="enable"
              v-model="editData.svr_enable"
              switch
              class="form-switch-md"
            >
              Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="editServerFunc" :disabled="!editData.name || !editData.ip || !editData.port || !editData.url"
          >Save</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
