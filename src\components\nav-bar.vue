<script setup>
import { onMounted } from "vue";
import { initTheme, switchTheme } from "@/utils/theme";
import { useRouter } from "vue-router";
import { useToast } from "vue-toastification";
import { clearCookieToken } from "@/utils/cookies";
import { authLogout } from "@/apis/e84_Api";
/**
 * Nav-bar Component
 */

const router = useRouter();
const toast = useToast();

initTheme();

const toggleHamburgerMenu = () => {
  const windowSize = document.documentElement.clientWidth;
  let layoutType = document.documentElement.getAttribute("data-layout");

  document.documentElement.setAttribute("data-sidebar-visibility", "show");
  let visiblilityType = document.documentElement.getAttribute(
    "data-sidebar-visibility"
  );

  if (windowSize > 767) {
    const hamburgerIcon = document.querySelector(".hamburger-icon");
    if (hamburgerIcon) {
      hamburgerIcon.classList.toggle("open");
    }
  }

  if (visiblilityType === "show" && layoutType === "vertical") {
    if (windowSize < 1025 && windowSize > 767) {
      document.body.classList.remove("vertical-sidebar-enable");
      document.documentElement.getAttribute("data-sidebar-size") == "sm"
        ? document.documentElement.setAttribute("data-sidebar-size", "")
        : document.documentElement.setAttribute("data-sidebar-size", "sm");
    } else if (windowSize > 1025) {
      document.body.classList.remove("vertical-sidebar-enable");
      document.documentElement.getAttribute("data-sidebar-size") == "lg"
        ? document.documentElement.setAttribute("data-sidebar-size", "sm")
        : document.documentElement.setAttribute("data-sidebar-size", "lg");
    } else if (windowSize <= 767) {
      document.body.classList.add("vertical-sidebar-enable");
      document.documentElement.setAttribute("data-sidebar-size", "lg");
    }
  }
};

const initFullScreen = () => {
  document.body.classList.toggle("fullscreen-enable");
  if (
    !document.fullscreenElement &&
    /* alternative standard method */
    !document.mozFullScreenElement &&
    !document.webkitFullscreenElement
  ) {
    // current working methods
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen();
    } else if (document.documentElement.mozRequestFullScreen) {
      document.documentElement.mozRequestFullScreen();
    } else if (document.documentElement.webkitRequestFullscreen) {
      document.documentElement.webkitRequestFullscreen(
        Element.ALLOW_KEYBOARD_INPUT
      );
    }
  } else {
    if (document.cancelFullScreen) {
      document.cancelFullScreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.webkitCancelFullScreen) {
      document.webkitCancelFullScreen();
    }
  }
};

const logOut = async () => {
  const result = await authLogout();
  try {
      clearCookieToken();
      toast.success(`Logout ${result.data.status}!`, {
        position: "bottom-right",
        timeout: 1000,
      });
      setTimeout(() => {
        router.push("login");
      }, 1000);
  } catch (error) {
    toast.error("Logout Failed!", {
      position: "bottom-right",
      timeout: 1000,
    });
  }
};

onMounted(() => {
  toggleHamburgerMenu();
  document.documentElement.getAttribute("data-sidebar-size") == "lg"
    ? document.documentElement.setAttribute("data-sidebar-size", "sm")
    : document.documentElement.setAttribute("data-sidebar-size", "lg");
  document.addEventListener("scroll", function () {
    var pageTopbar = document.getElementById("page-topbar");
    if (pageTopbar) {
      document.body.scrollTop >= 50 || document.documentElement.scrollTop >= 50
        ? pageTopbar.classList.add("topbar-shadow")
        : pageTopbar.classList.remove("topbar-shadow");
    }
  });
  if (document.getElementById("topnav-hamburger-icon")) {
    document
      .getElementById("topnav-hamburger-icon")
      .addEventListener("click", toggleHamburgerMenu);
  }
});
</script>

<template>
  <header id="page-topbar">
    <div class="layout-width">
      <div class="navbar-header">
        <div class="d-flex">
          <!-- LOGO -->
          <div class="navbar-brand-box horizontal-logo">
            <router-link to="/" class="logo logo-dark">
              <span class="logo-sm">
                <img
                  src="@/assets/images/logo/sys-icon-light.svg"
                  alt=""
                  height="22"
                />
              </span>
              <span class="logo-lg">
                <img
                  src="@/assets/images/logo/sys-logo-light.svg"
                  alt=""
                  height="35"
                />
              </span>
            </router-link>

            <router-link to="/" class="logo logo-light">
              <span class="logo-sm">
                <img
                  src="@/assets/images/logo/sys-icon-dark.svg"
                  alt=""
                  height="22"
                />
              </span>
              <span class="logo-lg">
                <img
                  src="@/assets/images/logo/sys-logo-dark.svg"
                  alt=""
                  height="25"
                />
              </span>
            </router-link>
          </div>

          <BButton
            variant="white"
            class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger"
            id="topnav-hamburger-icon"
          >
            <span class="hamburger-icon">
              <span></span>
              <span></span>
              <span></span>
            </span>
          </BButton>

          <!-- System Title -->
          <div class="header-item">
            <h1 class="position-relative mb-0 fs-22">
              GYRO-700 Management System
            </h1>
            
          </div>
        </div>

        <div class="d-flex align-items-center">
          <div class="ms-1 header-item d-none d-sm-flex">
            <BButton
              type="button"
              variant="ghost-secondary"
              class="btn-icon btn-topbar rounded-circle"
              data-toggle="fullscreen"
              @click="initFullScreen"
            >
              <i class="bx bx-fullscreen fs-22"></i>
            </BButton>
          </div>

          <div class="ms-1 header-item d-none d-sm-flex">
            <BButton
              type="button"
              variant="ghost-secondary"
              class="btn-icon btn-topbar rounded-circle light-dark-mode"
              @click="switchTheme()"
            >
              <i class="bx bx-moon fs-22"></i>
            </BButton>
          </div>

          <BDropdown
            variant="link"
            class="ms-sm-3 header-item topbar-user"
            toggle-class="rounded-circle arrow-none"
            menu-class="dropdown-menu-end"
            :offset="{ alignmentAxis: 0, crossAxis: 0, mainAxis: 5 }"
          >
            <template #button-content>
              <span class="d-flex align-items-center">
                <img
                  class="rounded-circle header-profile-user"
                  src="@/assets/images/users/gyro-avatar.svg"
                  alt="Header Avatar"
                />
                <span class="text-start ms-xl-2">
                  <span
                    class="d-none d-xl-inline-block ms-1 fw-medium user-name-text"
                    >Gyro Systems</span
                  >
                  <span class="d-none d-xl-block ms-1 fs-12 user-name-sub-text"
                    >Super User</span
                  >
                </span>
              </span>
            </template>
            <h6 class="dropdown-header">Welcome!</h6>
            <BButton class="dropdown-item" @click="logOut()"
              ><i class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i>
              <span class="align-middle" data-key="t-logout"> Logout</span>
            </BButton>
          </BDropdown>
        </div>
      </div>
    </div>
  </header>
</template>
