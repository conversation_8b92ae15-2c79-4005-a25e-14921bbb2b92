# IPC Add Modal Enhancement - Loadport Selection

## 🎯 **功能需求**
在 IPC Management 的 Add IPC Device Modal 中，為 Loadport 1-4 添加 checkbox 選擇功能，只有被選中的 Loadport 才會加入到最終的 API 請求中。

## 🛠️ **實現方案**

### 1. **資料結構修改**
```javascript
// 新增 Loadport 選擇狀態
const loadportSelection = reactive({
  port1: true,  // 預設選中
  port2: true,  // 預設選中
  port3: false, // 預設不選中
  port4: false  // 預設不選中
});
```

### 2. **功能函數**
```javascript
// 全選/取消全選功能
const toggleAllLoadports = () => {
  const allSelected = loadportSelection.port1 && loadportSelection.port2 && 
                     loadportSelection.port3 && loadportSelection.port4;
  const newValue = !allSelected;
  loadportSelection.port1 = newValue;
  loadportSelection.port2 = newValue;
  loadportSelection.port3 = newValue;
  loadportSelection.port4 = newValue;
};

// 計算選中數量
const selectedLoadportsCount = computed(() => {
  let count = 0;
  if (loadportSelection.port1) count++;
  if (loadportSelection.port2) count++;
  if (loadportSelection.port3) count++;
  if (loadportSelection.port4) count++;
  return count;
});
```

### 3. **API 請求邏輯修改**
```javascript
const addIPCFunc = async () => {
  // 根據選擇狀態動態構建 ports 陣列
  const selectedPorts = [];
  
  if (loadportSelection.port1) {
    selectedPorts.push({
      port_id: computedPort1.value,
      port_no: 1,
      dual_port: 0
    });
  }
  // ... 其他 port 的處理
  
  // 檢查是否至少選擇了一個 Loadport
  if (selectedPorts.length === 0) {
    toast.warning("Please select at least one Loadport");
    return;
  }
  
  const data = {
    device_id: addData.deviceId,
    name: addData.name,
    group: addData.group,
    ip: addData.ip,
    port: addData.port,
    ipc_enable: addData.ipcEnable,
    ports: selectedPorts  // 只包含選中的 ports
  };
};
```

### 4. **UI 界面改進**
- **Checkbox 控制**: 每個 Loadport 都有對應的 checkbox
- **輸入框狀態**: 未選中的 Loadport 輸入框會被禁用
- **全選按鈕**: 提供便捷的全選/取消全選功能
- **狀態顯示**: 顯示當前選中的 Loadport 數量 (x/4 Selected)

## 🎨 **UI 設計**

### Loadport Configuration Section
```html
<div class="d-flex justify-content-between align-items-center mb-3">
  <h6 class="mb-0">Loadport Configuration</h6>
  <div class="d-flex align-items-center">
    <span class="badge bg-info me-2">{{ selectedLoadportsCount }}/4 Selected</span>
    <BButton size="sm" variant="outline-primary" @click="toggleAllLoadports">
      {{ allLoadportsSelected ? 'Deselect All' : 'Select All' }}
    </BButton>
  </div>
</div>
```

### Individual Loadport Controls
```html
<div class="d-flex align-items-center mb-2">
  <BFormCheckbox
    id="port1Checkbox"
    v-model="loadportSelection.port1"
    class="me-2"
  />
  <label for="port1" class="form-label mb-0">Loadport 1</label>
</div>
<input
  v-model="computedPort1"
  type="text"
  class="form-control"
  id="port1"
  placeholder="Enter IPC Loadport 1"
  :disabled="!loadportSelection.port1"
/>
```

## ✅ **功能特點**

1. **選擇性創建**: 只有被選中的 Loadport 才會被創建
2. **輸入驗證**: 至少需要選擇一個 Loadport 才能提交
3. **視覺反饋**: 
   - 未選中的輸入框會被禁用
   - 顯示選中數量的徽章
   - 全選按鈕文字會動態變化
4. **預設值**: Port 1 和 Port 2 預設選中，Port 3 和 Port 4 預設不選中
5. **狀態重置**: Modal 關閉時會重置所有選擇狀態

## 🧪 **測試重點**

1. **基本功能測試**:
   - 選中/取消選中 checkbox 功能
   - 輸入框的啟用/禁用狀態
   - 全選/取消全選按鈕功能

2. **API 請求測試**:
   - 只選中部分 Loadport 時的 API 請求內容
   - 未選中任何 Loadport 時的錯誤提示
   - 選中所有 Loadport 時的完整請求

3. **狀態管理測試**:
   - Modal 重新打開時的狀態重置
   - 選中數量的正確計算和顯示

## 📁 **修改的文件**
- `src/views/dashboard/ipc/addModal.vue` - 主要實現文件

## 🎉 **預期效果**
用戶現在可以靈活選擇要創建哪些 Loadport，而不是強制創建所有 4 個 Loadport，提供更好的用戶體驗和更精確的設備配置控制。
