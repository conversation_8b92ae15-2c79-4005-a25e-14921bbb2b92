<script setup>
import { computed, reactive, ref, inject } from "vue";
import { useToast } from "vue-toastification";
import { editIpcDevice } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();

const deviceId = ref(null);
const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const editData = reactive({
  id: "",
  deviceId: "",
  name: "",
  group: "",
  ip: "",
  port: "",
  ipcEnable: true,
});

const computedPort1 = computed(() => {
  return `${editData.deviceId}_LP1`
});

const computedPort2 = computed(() => {
  return `${editData.deviceId}_LP2`
});

const computedPort3 = computed(() => {
  return `${editData.deviceId}_LP3`
});

const computedPort4 = computed(() => {
  return `${editData.deviceId}_LP4`
});


const showModal = (data) => {
  // console.log("Device ID:", data);
  editData.id = data.id;
  editData.deviceId = data.device_id;
  editData.name = data.name;
  editData.group = data.group;
  editData.ip = data.ip;
  editData.port = data.port;
  editData.ipcEnable = data.ipc_enable;
  modalShow.value = true;
};

const addIPCFunc = async () => {
  console.log("Edit Device:", deviceId.value);
  try {
    const data = {
      id: editData.id,
      device_id: editData.deviceId,
      name: editData.name,
      group: editData.group,
      ip: editData.ip,
      port: editData.port,
      ipc_enable: editData.ipcEnable,
      ports: [
        {
          port_id: computedPort1.value,
          port_no: 1,
          dual_port: 0
        },
        {
          port_id: computedPort2.value,
          port_no: 2,
          dual_port: 0
        },
        {
          port_id: computedPort3.value,
          port_no: 3,
          dual_port: 0
        },
        {
          port_id: computedPort4.value,
          port_no: 4,
          dual_port: 0
        }
      ]
    };
    console.log("EDIT DATA:", data);

    const res = await editIpcDevice(data);
    console.log("Edit Device:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Edit Device ${editData.deviceId} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Edit IPC Device"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3 mb-3">
        <BCol xxl="6">
          <div>
            <label for="deviceId" class="form-label">Device ID</label>
            <input
              v-model="editData.deviceId"
              type="text"
              class="form-control"
              id="deviceId"
              placeholder="Enter Device ID"
            />
          </div>
        </BCol>
        <BCol xxl="6">
          <div>
            <label for="name" class="form-label">Name</label>
            <input
              v-model="editData.name"
              type="text"
              class="form-control"
              id="name"
              placeholder="Enter name"
            />
          </div>
        </BCol>
        <BCol xxl="4">
          <label for="group" class="form-label">Group</label>
          <input
            v-model="editData.group"
            type="text"
            class="form-control"
            id="group"
            placeholder="Enter IPC Group"
          />
        </BCol>
        <BCol xxl="5">
          <label for="ip" class="form-label">IP</label>
          <input
            v-model="editData.ip"
            type="text"
            class="form-control"
            id="ip"
            placeholder="Enter IPC IP Address"
          />
        </BCol>
        <BCol xxl="3">
          <label for="port" class="form-label">Port</label>
          <input
            v-model="editData.port"
            type="text"
            class="form-control"
            id="port"
            placeholder="Enter IPC Port"
          />
        </BCol>
      </BRow>
      <BRow class="g-3">
        <BCol xxl="6">
          <label for="port1" class="form-label">Loadport 1</label>
            <input
              v-model="computedPort1"
              type="text"
              class="form-control"
              id="port1"
              placeholder="Enter IPC Loadport 1"
            />  
        </BCol>
        <BCol xxl="6">
          <label for="port2" class="form-label">Loadport 2</label>
            <input
              v-model="computedPort2"
              type="text"
              class="form-control"
              id="port2"
              placeholder="Enter IPC Loadport 2"
            />  
        </BCol>
        <BCol xxl="6">
          <label for="port3" class="form-label">Loadport 3</label>
            <input
              v-model="computedPort3"
              type="text"
              class="form-control"
              id="port3"
              placeholder="Enter IPC Loadport 3"
            />  
        </BCol>
        <BCol xxl="6">
          <label for="port4" class="form-label">Loadport 4</label>
            <input
              v-model="computedPort4"
              type="text"
              class="form-control"
              id="port4"
              placeholder="Enter IPC Loadport 4"
            />  
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          <BFormGroup class="form-switch form-switch-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="ipcEnableSwitch"
              v-model="editData.ipcEnable"
              switch
              class="form-switch-md me-2"
            >
              IPC Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="addIPCFunc" :disabled="!editData.deviceId || !editData.name || !editData.group || !editData.ip || !editData.port"
          >Save</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
