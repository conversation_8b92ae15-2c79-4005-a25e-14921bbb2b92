<script setup>
import { computed, reactive, ref, inject } from "vue";
import { useToast } from "vue-toastification";
import { editIpcDevice, ipcStatus } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();

const deviceId = ref(null);
const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const editData = reactive({
  id: "",
  deviceId: "",
  name: "",
  group: "",
  ip: "",
  port: "",
  ipcEnable: true,
});

// Loadport 資料和選擇狀態
const loadportData = reactive({
  port1: {
    selected: false,
    port_id: "",
    port_no: 1,
    dual_port: 0
  },
  port2: {
    selected: false,
    port_id: "",
    port_no: 2,
    dual_port: 0
  },
  port3: {
    selected: false,
    port_id: "",
    port_no: 3,
    dual_port: 0
  },
  port4: {
    selected: false,
    port_id: "",
    port_no: 4,
    dual_port: 0
  }
});

// 全選/取消全選 Loadport
const toggleAllLoadports = () => {
  const allSelected = loadportData.port1.selected && loadportData.port2.selected &&
                     loadportData.port3.selected && loadportData.port4.selected;

  const newValue = !allSelected;
  loadportData.port1.selected = newValue;
  loadportData.port2.selected = newValue;
  loadportData.port3.selected = newValue;
  loadportData.port4.selected = newValue;
};

// 計算是否全選
const allLoadportsSelected = computed(() => {
  return loadportData.port1.selected && loadportData.port2.selected &&
         loadportData.port3.selected && loadportData.port4.selected;
});

// 計算選中的 Loadport 數量
const selectedLoadportsCount = computed(() => {
  let count = 0;
  if (loadportData.port1.selected) count++;
  if (loadportData.port2.selected) count++;
  if (loadportData.port3.selected) count++;
  if (loadportData.port4.selected) count++;
  return count;
});

const showModal = async (data) => {
  console.log("Edit Modal - Received data:", data);

  editData.id = data.id;
  editData.deviceId = data.device_id;
  editData.name = data.name;
  editData.group = data.group;
  editData.ip = data.ip;
  editData.port = data.port;
  editData.ipcEnable = data.ipc_enable;

  // 重置所有 Loadport 資料
  loadportData.port1.selected = false;
  loadportData.port1.port_id = "";
  loadportData.port2.selected = false;
  loadportData.port2.port_id = "";
  loadportData.port3.selected = false;
  loadportData.port3.port_id = "";
  loadportData.port4.selected = false;
  loadportData.port4.port_id = "";

  try {
    // 獲取詳細的 IPC 狀態資料，包含 loadports 資訊
    console.log("Edit Modal - Fetching detailed IPC status...");
    const statusRes = await ipcStatus();
    const ipcStatusData = statusRes.data.ipc_status;

    // 尋找當前設備的詳細資訊
    const deviceDetail = ipcStatusData.find(device => device.device_id === data.device_id);

    if (deviceDetail && deviceDetail.loadports) {
      console.log("Edit Modal - Found device detail with loadports:", deviceDetail.loadports);

      // 根據實際的 loadports 資料初始化
      Object.values(deviceDetail.loadports).forEach(loadport => {
        const portKey = `port${loadport.port_no}`;
        if (loadportData[portKey]) {
          loadportData[portKey].selected = true;
          loadportData[portKey].port_id = loadport.port_id;
          loadportData[portKey].port_no = loadport.port_no;
          loadportData[portKey].dual_port = loadport.dual_port || 0;
        }
      });
    } else {
      console.log("Edit Modal - No loadports found for device, using default initialization");
      // 如果沒有找到 loadports，提供預設值但不選中
      // 用戶可以手動選擇和修改
    }
  } catch (error) {
    console.error("Edit Modal - Error fetching device details:", error);
    // 如果獲取詳細資訊失敗，不影響 Modal 的開啟
    // 用戶可以手動配置 Loadport
  }

  console.log("Edit Modal - Final loadport data:", loadportData);

  modalShow.value = true;
};

const addIPCFunc = async () => {
  console.log("Edit Device:", deviceId.value);
  console.log("Loadport Data:", loadportData);

  // 根據 loadportData 動態構建 ports 陣列
  const ports = [];

  if (loadportData.port1.selected && loadportData.port1.port_id.trim()) {
    ports.push({
      port_id: loadportData.port1.port_id,
      port_no: loadportData.port1.port_no,
      dual_port: loadportData.port1.dual_port
    });
  }

  if (loadportData.port2.selected && loadportData.port2.port_id.trim()) {
    ports.push({
      port_id: loadportData.port2.port_id,
      port_no: loadportData.port2.port_no,
      dual_port: loadportData.port2.dual_port
    });
  }

  if (loadportData.port3.selected && loadportData.port3.port_id.trim()) {
    ports.push({
      port_id: loadportData.port3.port_id,
      port_no: loadportData.port3.port_no,
      dual_port: loadportData.port3.dual_port
    });
  }

  if (loadportData.port4.selected && loadportData.port4.port_id.trim()) {
    ports.push({
      port_id: loadportData.port4.port_id,
      port_no: loadportData.port4.port_no,
      dual_port: loadportData.port4.dual_port
    });
  }
  
  // 檢查是否至少選擇了一個 Loadport
  if (ports.length === 0) {
    toast.warning('請至少選擇一個 Loadport', {
      position: "bottom-right",
      timeout: 2000,
    });
    return;
  }
  
  try {
    const data = {
      id: editData.id,
      device_id: editData.deviceId,
      name: editData.name,
      group: editData.group,
      ip: editData.ip,
      port: editData.port,
      ipc_enable: editData.ipcEnable,
      ports: ports
    };
    console.log("EDIT DATA:", data);

    const res = await editIpcDevice(data);
    console.log("Edit Device:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Edit Device ${editData.deviceId} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Edit IPC Device"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3 mb-3">
        <BCol xxl="6">
          <div>
            <label for="deviceId" class="form-label">Device ID</label>
            <input
              v-model="editData.deviceId"
              type="text"
              class="form-control"
              id="deviceId"
              placeholder="Enter Device ID"
            />
          </div>
        </BCol>
        <BCol xxl="6">
          <div>
            <label for="name" class="form-label">Name</label>
            <input
              v-model="editData.name"
              type="text"
              class="form-control"
              id="name"
              placeholder="Enter name"
            />
          </div>
        </BCol>
        <BCol xxl="4">
          <label for="group" class="form-label">Group</label>
          <input
            v-model="editData.group"
            type="text"
            class="form-control"
            id="group"
            placeholder="Enter IPC Group"
          />
        </BCol>
        <BCol xxl="5">
          <label for="ip" class="form-label">IP</label>
          <input
            v-model="editData.ip"
            type="text"
            class="form-control"
            id="ip"
            placeholder="Enter IPC IP Address"
          />
        </BCol>
        <BCol xxl="3">
          <label for="port" class="form-label">Port</label>
          <input
            v-model="editData.port"
            type="text"
            class="form-control"
            id="port"
            placeholder="Enter IPC Port"
          />
        </BCol>
      </BRow>
      <!-- Loadport Selection Section -->
      <BRow class="mt-4">
        <BCol xxl="12">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">Loadport Configuration</h6>
            <div class="d-flex align-items-center">
              <span class="badge bg-info me-2">{{ selectedLoadportsCount }}/4 Selected</span>
              <BButton
                size="sm"
                variant="outline-primary"
                @click="toggleAllLoadports"
              >
                {{ allLoadportsSelected ? 'Deselect All' : 'Select All' }}
              </BButton>
            </div>
          </div>
        </BCol>
      </BRow>
      <BRow class="g-3">
        <BCol xxl="6">
          <div class="d-flex align-items-center mb-2">
            <BFormCheckbox
              id="port1Checkbox"
              v-model="loadportData.port1.selected"
              class="me-2"
            />
            <label for="port1" class="form-label mb-0">Loadport 1</label>
          </div>
          <input
            v-model="loadportData.port1.port_id"
            type="text"
            class="form-control"
            id="port1"
            placeholder="Enter IPC Loadport 1"
            :disabled="!loadportData.port1.selected"
          />
        </BCol>
        <BCol xxl="6">
          <div class="d-flex align-items-center mb-2">
            <BFormCheckbox
              id="port2Checkbox"
              v-model="loadportData.port2.selected"
              class="me-2"
            />
            <label for="port2" class="form-label mb-0">Loadport 2</label>
          </div>
          <input
            v-model="loadportData.port2.port_id"
            type="text"
            class="form-control"
            id="port2"
            placeholder="Enter IPC Loadport 2"
            :disabled="!loadportData.port2.selected"
          />
        </BCol>
        <BCol xxl="6">
          <div class="d-flex align-items-center mb-2">
            <BFormCheckbox
              id="port3Checkbox"
              v-model="loadportData.port3.selected"
              class="me-2"
            />
            <label for="port3" class="form-label mb-0">Loadport 3</label>
          </div>
          <input
            v-model="loadportData.port3.port_id"
            type="text"
            class="form-control"
            id="port3"
            placeholder="Enter IPC Loadport 3"
            :disabled="!loadportData.port3.selected"
          />
        </BCol>
        <BCol xxl="6">
          <div class="d-flex align-items-center mb-2">
            <BFormCheckbox
              id="port4Checkbox"
              v-model="loadportData.port4.selected"
              class="me-2"
            />
            <label for="port4" class="form-label mb-0">Loadport 4</label>
          </div>
          <input
            v-model="loadportData.port4.port_id"
            type="text"
            class="form-control"
            id="port4"
            placeholder="Enter IPC Loadport 4"
            :disabled="!loadportData.port4.selected"
          />
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          <BFormGroup class="form-switch form-switch-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="ipcEnableSwitch"
              v-model="editData.ipcEnable"
              switch
              class="form-switch-md me-2"
            >
              IPC Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="addIPCFunc" :disabled="!editData.deviceId || !editData.name || !editData.group || !editData.ip || !editData.port"
          >Save</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
