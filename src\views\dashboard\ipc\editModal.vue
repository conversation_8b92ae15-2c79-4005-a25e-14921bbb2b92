<script setup>
import { computed, reactive, ref, inject } from "vue";
import { useToast } from "vue-toastification";
import { editIpcDevice } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();

const deviceId = ref(null);
const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const editData = reactive({
  id: "",
  deviceId: "",
  name: "",
  group: "",
  ip: "",
  port: "",
  ipcEnable: true,
});

const loadportSelection = reactive({
  port1: false,
  port2: false,
  port3: false,
  port4: false,
});

// 全選/取消全選 Loadport
const toggleAllLoadports = () => {
  const allSelected = loadportSelection.port1 && loadportSelection.port2 &&
                     loadportSelection.port3 && loadportSelection.port4;

  const newValue = !allSelected;
  loadportSelection.port1 = newValue;
  loadportSelection.port2 = newValue;
  loadportSelection.port3 = newValue;
  loadportSelection.port4 = newValue;
};

// 計算是否全選
const allLoadportsSelected = computed(() => {
  return loadportSelection.port1 && loadportSelection.port2 &&
         loadportSelection.port3 && loadportSelection.port4;
});

// 計算選中的 Loadport 數量
const selectedLoadportsCount = computed(() => {
  let count = 0;
  if (loadportSelection.port1) count++;
  if (loadportSelection.port2) count++;
  if (loadportSelection.port3) count++;
  if (loadportSelection.port4) count++;
  return count;
});

const computedPort1 = computed(() => {
  return loadportSelection.port1 ? `${editData.deviceId}_LP1` : ''
});

const computedPort2 = computed(() => {
  return loadportSelection.port2 ? `${editData.deviceId}_LP2` : ''
});

const computedPort3 = computed(() => {
  return loadportSelection.port3 ? `${editData.deviceId}_LP3` : ''
});

const computedPort4 = computed(() => {
  return loadportSelection.port4 ? `${editData.deviceId}_LP4` : ''
});


const showModal = (data) => {
  // console.log("Device ID:", data);
  editData.id = data.id;
  editData.deviceId = data.device_id;
  editData.name = data.name;
  editData.group = data.group;
  editData.ip = data.ip;
  editData.port = data.port;
  editData.ipcEnable = data.ipc_enable;
  
  // 根據 loadports 資料初始化 loadportSelection
  if (data.loadports) {
    loadportSelection.port1 = !!data.loadports['1'];
    loadportSelection.port2 = !!data.loadports['2'];
    loadportSelection.port3 = !!data.loadports['3'];
    loadportSelection.port4 = !!data.loadports['4'];
  } else {
    // 如果沒有 loadports 資料，預設全部未選中
    loadportSelection.port1 = false;
    loadportSelection.port2 = false;
    loadportSelection.port3 = false;
    loadportSelection.port4 = false;
  }
  
  modalShow.value = true;
};

const addIPCFunc = async () => {
  console.log("Edit Device:", deviceId.value);
  
  // 根據 loadportSelection 動態構建 ports 陣列
  const ports = [];
  if (loadportSelection.port1) {
    ports.push({
      port_id: computedPort1.value,
      port_no: 1,
      dual_port: 0
    });
  }
  if (loadportSelection.port2) {
    ports.push({
      port_id: computedPort2.value,
      port_no: 2,
      dual_port: 0
    });
  }
  if (loadportSelection.port3) {
    ports.push({
      port_id: computedPort3.value,
      port_no: 3,
      dual_port: 0
    });
  }
  if (loadportSelection.port4) {
    ports.push({
      port_id: computedPort4.value,
      port_no: 4,
      dual_port: 0
    });
  }
  
  // 檢查是否至少選擇了一個 Loadport
  if (ports.length === 0) {
    toast.warning('請至少選擇一個 Loadport', {
      position: "bottom-right",
      timeout: 2000,
    });
    return;
  }
  
  try {
    const data = {
      id: editData.id,
      device_id: editData.deviceId,
      name: editData.name,
      group: editData.group,
      ip: editData.ip,
      port: editData.port,
      ipc_enable: editData.ipcEnable,
      ports: ports
    };
    console.log("EDIT DATA:", data);

    const res = await editIpcDevice(data);
    console.log("Edit Device:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Edit Device ${editData.deviceId} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Edit IPC Device"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3 mb-3">
        <BCol xxl="6">
          <div>
            <label for="deviceId" class="form-label">Device ID</label>
            <input
              v-model="editData.deviceId"
              type="text"
              class="form-control"
              id="deviceId"
              placeholder="Enter Device ID"
            />
          </div>
        </BCol>
        <BCol xxl="6">
          <div>
            <label for="name" class="form-label">Name</label>
            <input
              v-model="editData.name"
              type="text"
              class="form-control"
              id="name"
              placeholder="Enter name"
            />
          </div>
        </BCol>
        <BCol xxl="4">
          <label for="group" class="form-label">Group</label>
          <input
            v-model="editData.group"
            type="text"
            class="form-control"
            id="group"
            placeholder="Enter IPC Group"
          />
        </BCol>
        <BCol xxl="5">
          <label for="ip" class="form-label">IP</label>
          <input
            v-model="editData.ip"
            type="text"
            class="form-control"
            id="ip"
            placeholder="Enter IPC IP Address"
          />
        </BCol>
        <BCol xxl="3">
          <label for="port" class="form-label">Port</label>
          <input
            v-model="editData.port"
            type="text"
            class="form-control"
            id="port"
            placeholder="Enter IPC Port"
          />
        </BCol>
      </BRow>
      <!-- Loadport Selection Section -->
      <BRow class="mt-4">
        <BCol xxl="12">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">Loadport Configuration</h6>
            <div class="d-flex align-items-center">
              <span class="badge bg-info me-2">{{ selectedLoadportsCount }}/4 Selected</span>
              <BButton
                size="sm"
                variant="outline-primary"
                @click="toggleAllLoadports"
              >
                {{ allLoadportsSelected ? 'Deselect All' : 'Select All' }}
              </BButton>
            </div>
          </div>
        </BCol>
      </BRow>
      <BRow class="g-3">
        <BCol xxl="6">
          <div class="d-flex align-items-center mb-2">
            <BFormCheckbox
              id="port1Checkbox"
              v-model="loadportSelection.port1"
              class="me-2"
            />
            <label for="port1" class="form-label mb-0">Loadport 1</label>
          </div>
          <input
            v-model="computedPort1"
            type="text"
            class="form-control"
            id="port1"
            placeholder="Enter IPC Loadport 1"
            :disabled="!loadportSelection.port1"
          />
        </BCol>
        <BCol xxl="6">
          <div class="d-flex align-items-center mb-2">
            <BFormCheckbox
              id="port2Checkbox"
              v-model="loadportSelection.port2"
              class="me-2"
            />
            <label for="port2" class="form-label mb-0">Loadport 2</label>
          </div>
          <input
            v-model="computedPort2"
            type="text"
            class="form-control"
            id="port2"
            placeholder="Enter IPC Loadport 2"
            :disabled="!loadportSelection.port2"
          />
        </BCol>
        <BCol xxl="6">
          <div class="d-flex align-items-center mb-2">
            <BFormCheckbox
              id="port3Checkbox"
              v-model="loadportSelection.port3"
              class="me-2"
            />
            <label for="port3" class="form-label mb-0">Loadport 3</label>
          </div>
          <input
            v-model="computedPort3"
            type="text"
            class="form-control"
            id="port3"
            placeholder="Enter IPC Loadport 3"
            :disabled="!loadportSelection.port3"
          />
        </BCol>
        <BCol xxl="6">
          <div class="d-flex align-items-center mb-2">
            <BFormCheckbox
              id="port4Checkbox"
              v-model="loadportSelection.port4"
              class="me-2"
            />
            <label for="port4" class="form-label mb-0">Loadport 4</label>
          </div>
          <input
            v-model="computedPort4"
            type="text"
            class="form-control"
            id="port4"
            placeholder="Enter IPC Loadport 4"
            :disabled="!loadportSelection.port4"
          />
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          <BFormGroup class="form-switch form-switch-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="ipcEnableSwitch"
              v-model="editData.ipcEnable"
              switch
              class="form-switch-md me-2"
            >
              IPC Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="addIPCFunc" :disabled="!editData.deviceId || !editData.name || !editData.group || !editData.ip || !editData.port"
          >Save</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
