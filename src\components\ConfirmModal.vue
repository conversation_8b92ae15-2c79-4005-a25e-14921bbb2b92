<script setup>
import { computed } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  operation: {
    type: String,
    default: 'Reset',
    validator: (value) => !value || ['Reset', 'Auto', 'Manual'].includes(value)
  },
  tableMode: {
    type: String,
    default: 'Loadport',
    validator: (value) => !value || ['Loadport', 'IPC'].includes(value)
  },
  selectedItems: {
    type: Array,
    default: () => []
  },
  ids: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:show', 'confirm', 'cancel']);

// 計算模式文字
const modeText = computed(() => {
  return props.tableMode === 'Loadport' ? 'Loadport' : 'IPC Device';
});

// 計算操作描述
const operationDescription = computed(() => {
  switch (props.operation) {
    case 'Reset':
      return 'reset all ports';
    case 'Auto':
      return 'enable Auto mode';
    case 'Manual':
      return 'enable Manual mode';
    default:
      return 'perform operation';
  }
});

// 計算操作圖標和顏色
const operationConfig = computed(() => {
  switch (props.operation) {
    case 'Reset':
      return {
        icon: 'ri-restart-line',
        color: 'danger',
        bgColor: 'bg-danger-subtle'
      };
    case 'Auto':
      return {
        icon: 'ri-play-circle-line',
        color: 'success',
        bgColor: 'bg-success-subtle'
      };
    case 'Manual':
      return {
        icon: 'ri-hand-coin-line',
        color: 'primary',
        bgColor: 'bg-primary-subtle'
      };
    default:
      return {
        icon: 'ri-information-line',
        color: 'info',
        bgColor: 'bg-info-subtle'
      };
  }
});

// 處理確認
const handleConfirm = () => {
  emit('confirm');
  closeModal();
};

// 處理取消
const handleCancel = () => {
  emit('cancel');
  closeModal();
};

// 關閉 Modal
const closeModal = () => {
  emit('update:show', false);
};
</script>

<template>
  <BModal
    :model-value="show"
    @update:model-value="(value) => { if (!value) closeModal(); }"
    size="md"
    hide-footer
    centered
    no-close-on-backdrop
    :title="`Confirm ${operation} Operation`"
    class="confirm-modal modal-stacked"
  >
    <div class="modal-body p-4">
      <!-- 操作圖標和標題 -->
      <div class="text-center mb-4">
        <div 
          :class="[
            'rounded-circle d-inline-flex align-items-center justify-content-center mb-3',
            operationConfig.bgColor
          ]"
          style="width: 64px; height: 64px;"
        >
          <i 
            :class="[operationConfig.icon, `text-${operationConfig.color}`, 'fs-1']"
          ></i>
        </div>
        <h5 class="mb-2">Confirm {{ operation }} Operation</h5>
        <p class="text-muted mb-0">
          Are you sure you want to {{ operationDescription }} for {{ selectedItems.length }} {{ modeText }}{{ selectedItems.length > 1 ? 's' : '' }}?
        </p>
      </div>

      <!-- ID 列表 -->
      <div class="mb-4">
        <label class="form-label fw-semibold">
          {{ tableMode === 'Loadport' ? 'Port IDs' : 'Device IDs' }} to be affected:
        </label>
        <div class="border rounded p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
          <div class="row g-2">
            <div 
              v-for="(id, index) in ids" 
              :key="index"
              class="col-auto"
            >
              <span :class="[
                'badge rounded-pill',
                `text-bg-${operationConfig.color}`
              ]">
                {{ id }}
              </span>
            </div>
          </div>
          
          <!-- 如果 ID 很多，顯示總數 -->
          <div v-if="ids.length > 10" class="mt-2 text-center">
            <small class="text-muted">
              Total: {{ ids.length }} {{ tableMode === 'Loadport' ? 'ports' : 'devices' }}
            </small>
          </div>
        </div>
      </div>

      <!-- 警告提示 -->
      <div class="alert alert-warning d-flex align-items-center" role="alert">
        <i class="ri-alert-line me-2"></i>
        <div>
          <strong>Warning:</strong> This action cannot be undone. Please confirm that you want to proceed with the {{ operation.toLowerCase() }} operation.
        </div>
      </div>

      <!-- 操作按鈕 -->
      <div class="d-flex justify-content-end gap-2">
        <BButton
          variant="outline-secondary"
          @click="handleCancel"
        >
          <i class="ri-close-line align-middle me-1"></i>
          Cancel
        </BButton>
        <BButton
          :variant="operationConfig.color"
          @click="handleConfirm"
        >
          <i :class="[operationConfig.icon, 'align-middle me-1']"></i>
          Confirm {{ operation }}
        </BButton>
      </div>
    </div>
  </BModal>
</template>

<style scoped>
.confirm-modal :deep(.modal-header) {
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 1rem;
}

.confirm-modal :deep(.modal-title) {
  font-weight: 600;
  color: #495057;
}

.badge {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

.alert {
  border-left: 4px solid #ff6b35;
}
</style>