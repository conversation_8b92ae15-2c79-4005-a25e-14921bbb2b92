<script setup>
import { ref, inject, reactive } from "vue";
import {
    useToast
} from "vue-toastification";
import { deleteAccountData } from "@/apis/e84_Api";

const props = defineProps(["dataIcon", "dataShow", "dataMultiShow"]);
const toast = useToast();

const modalIcon = ref(props.dataIcon);
const getData = reactive({
    userid: "",
    name: "",
    acc_type: "",
});
const modalShow = ref(props.dataShow);
const multiModalShow = ref(props.dataMultiShow);

const refreshData = inject('refreshData')

const showModal = (data) => {
    // console.log("Device ID:", data);
    getData.userid = data.userid;
    getData.name = data.name;
    getData.acc_type = data.acc_type;
    modalShow.value = true;
};

const showMultiModal = (data) => {
    // console.log("Device ID:", data);
    getData.userid = data;
    multiModalShow.value = true;
};

const deleteFunc = async () => {
    console.log("Delete User:", getData);
    try {
        const data = {
          userid : getData.userid
        }
        console.log("Delete User ID:", data)

        const res = await deleteAccountData(data);
        console.log("Delete User Res:", res);

        if(res.status >= 200 && res.status < 300) {
            toast.success(`Delete ${getData.userid} Success` , {
                position: "bottom-right",
                timeout: 1000,
            });

            refreshData()
        }

    } catch (error) {
      console.log("ERROR", error);
      toast.error(
        `ERROR ${error.status}: [${error}. ]`,
        {
          position: "bottom-right",
          timeout: 1000,
        }
      );
    }

    modalShow.value = false;
};

defineExpose({ showModal, showMultiModal });

</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body text-center pt-0">
      <i :class="[modalIcon]" class="fs-c-10 text-danger-emphasis"></i>
      <div class="mt-0 pt-0">
        <h4>Are you sure to Delete <br> User <span class="text-danger-emphasis">{{ getData.userid }}</span>?</h4>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
      <BButton type="button" variant="danger" class="me-5" @click="deleteFunc">Yes</BButton>
      <BButton type="button" variant="light" @click="modalShow = false"
        >Close
      </BButton>
    </div>
    </div>
  </BModal>
  <BModal
    v-model="multiModalShow"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body text-center pt-0">
      <i :class="[modalIcon]" class="fs-c-10 text-danger-emphasis"></i>
      <div class="mt-0 pt-0">
        <h4>Are you sure to Delete SF <span class="text-danger-emphasis">{{ getData.sf }}</span> Code  <span class="text-danger-emphasis">{{ getData.code }}</span>？</h4>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
        <BButton type="button" variant="danger" class="me-5" @click="deleteFunc">Yes</BButton>
        <BButton type="button" variant="light" @click="modalShow = false"
          >Close
        </BButton>
      </div>
    </div>
  </BModal>
</template>