<script setup>
import { ref } from "vue";
import Layout from "@/layouts/main.vue";
import PageHeader from "@/components/page-header.vue";
import SystemTable from "./systemTabulator.vue";
import AlarmTable from "./alarmTabulator.vue";
import IpcTable from "./ipcTabulator.vue";

const alarmTabData = ref(null);
const systemTabData = ref(null);
const ipcTabData = ref(null);

const showAlarmTab = () => {
  console.log("Alarm Tab Clicked");
    alarmTabData.value.refreshTable();
};

const showSystemTab = () => {
  console.log("System Tab Clicked");
    systemTabData.value.refreshTable();
};

const showIpcTab = () => {
  console.log("IPC Tab Clicked");
    ipcTabData.value.getIpcListData();
    ipcTabData.value.refreshTable();
};

</script>
<template>
<Layout>
    <PageHeader title="Log Management" pageTitle="Main Dashboards" />
    <BTabs nav-class="mb-3" pills justified>
        <BTab title="System Logs" active @click="showSystemTab">
          <div class="text-muted">
            <div class="w-100">
              <SystemTable ref="systemTabData" />
            </div>
          </div>
        </BTab>
        <BTab title="Alarm Logs" @click="showAlarmTab">
          <div class="text-muted">
            <div class="w-100">
                <AlarmTable ref="alarmTabData" />
            </div>
          </div>
        </BTab>
        <BTab title="IPC Logs" @click="showIpcTab">
          <div class="text-muted">
            <div class="w-100">
              <IpcTable ref="ipcTabData" />
            </div>
          </div>
        </BTab>
      </BTabs>
</Layout>
</template>