import axios from 'axios'

// Get Config Data
const getConfig = async () => {
  return await axios
    .get(
      `http://${location.host}/config.json?timestamp=${new Date().getTime()}`
    )
    .then((res) => {
      const resConfig = res.data;
      const localConfig = localStorage.getItem("serverConfig") || [];

      console.log("CHECK CONFIG: ", resConfig);
      console.log("CHECK LOCAL CONFIG: ", resConfig);


      const areConfigsEqual = JSON.stringify(localConfig) === JSON.stringify(resConfig);

      if (!areConfigsEqual) {
        localStorage.setItem('serverConfig', JSON.stringify(resConfig));
      }
      
      return resConfig
    })
    .catch((err) => {
      console.error("Axios Create Instance Error: ", err);
    });
};

export default getConfig