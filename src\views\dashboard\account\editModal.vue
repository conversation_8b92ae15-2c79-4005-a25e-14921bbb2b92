<script setup>
import { reactive, ref, inject, watch } from "vue";
import { useToast } from "vue-toastification";
import { editAccountData } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();

const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const editData = reactive({
  userid: "",
  name: "",
  acc_type: "",
  active: true,
  password: "",
  passwordConfirm: ""
});


const showModal = (data) => {
  // console.log("Device ID:", data);
  editData.userid = data.userid;
  editData.name = data.name;
  editData.acc_type = data.acc_type;
  editData.password = data.password;
  editData.passwordConfirm = data.passwordConfirm;
  editData.active = data.active;
  
  modalShow.value = true;
};

const editAccountFunc = async () => {
  // console.log("Edit Account:", editData);
  try {
    const data = {
      userid: editData.userid,
      name: editData.name,
      acc_type: editData.acc_type,
      password: editData.password,
      passwordConfirm: editData.passwordConfirm,
      active: editData.active,
    };
    console.log("EDIT Account Post Data:", data);

    const res = await editAccountData(data);
    console.log("Edit Account Res:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Edit Account ${editData.name} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

const showMismatchMessage = ref(false)
const passwordTooShort = ref(false)
let mismatchTimeout = null

const checkPasswordMismatch = () => {
  // 檢查密碼長度
  if (editData.password) {
    passwordTooShort.value = editData.password.length < 8
  } else {
    passwordTooShort.value = false
  }

  // 檢查密碼和確認密碼是否匹配
  if (editData.password && editData.passwordConfirm) {
    showMismatchMessage.value = editData.password !== editData.passwordConfirm
  } else {
    showMismatchMessage.value = false
  }
}


const resetForm = () => {
      showMismatchMessage.value = false
      passwordTooShort.value = false
    }

watch(editData, () => {
  console.log("CHECK")
  if (mismatchTimeout) clearTimeout(mismatchTimeout)
  mismatchTimeout = setTimeout(checkPasswordMismatch, 1000)
})

watch(
  () => modalShow.value,
  (newValue) => {
    if (newValue === false) {
      resetForm();
    }
  }
);

defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Edit Account"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3">
        <BCol xxl="6">
          <div>
            <label for="acc_type" class="form-label">User Type</label>
            <BFormSelect v-model="editData.acc_type" class="form-select mb-3"
                    aria-label="Default select">
                    <BFormSelectOption :value="null">Select User Type</BFormSelectOption>
                    <BFormSelectOption value="ADMIN">ADMIN</BFormSelectOption>
                    <BFormSelectOption value="ME">ME</BFormSelectOption>
                    <BFormSelectOption value="OP">OP</BFormSelectOption>
                    <BFormSelectOption value="PE">PE</BFormSelectOption>
                  </BFormSelect>
          </div>
        </BCol>
      </BRow>
      <BRow class="g-3 mb-3">
        <BCol xxl="6">
          <div>
            <label for="userid" class="form-label">User ID</label>
            <input
              v-model="editData.userid"
              type="string"
              class="form-control"
              id="userid"
              placeholder="Enter your user ID"
            />
          </div>
        </BCol>
        <BCol xxl="6">
          <label for="name" class="form-label">Name</label>
          <input
            v-model="editData.name"
            type="string"
            class="form-control"
            id="name"
            placeholder="Enter your name"
          />
        </BCol>
        <BCol xxl="6">
          <label for="password" class="form-label">Password</label>
          <input
            v-model="editData.password"
            type="text"
            class="form-control"
            id="password"
            placeholder="Enter your password"
          />
          <div v-if="passwordTooShort" class="text-danger">
            Password must be at least 8 characters
          </div>
        </BCol>
        <BCol xxl="6">
          <label for="passwordConfirm" class="form-label">Confirm Password</label>
          <input
            v-model="editData.passwordConfirm"
            type="text"
            class="form-control"
            id="passwordConfirm"
            placeholder="Confirm your password"
          />
          <div v-if="showMismatchMessage" class="text-danger">
            Passwords do not match
          </div>
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          
        </BCol>
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="active"
              v-model="editData.active"
              switch
              class="form-switch-md me-2"
            >
              Active
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="editAccountFunc" :disabled="!editData.userid || !editData.name || !editData.acc_type"
          >Save</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
