# Edit Modal 正確實現 - 基於實際資料的 Loadport 選擇

## 🎯 **用戶需求重新分析**

用戶指出我的實現方式不對，主要問題：
1. **不應該假設現有設備都有 4 個 Loadport**
2. **不應該預設全選**
3. **應該以獲取的實際資料為主**

## 🔍 **資料流分析**

### **資料來源問題**：
1. **ipcGetAll API** - 返回基本設備列表，可能不包含詳細的 ports 資訊
2. **ipcStatus API** - 返回詳細狀態，包含 loadports 資訊
3. **Edit Modal 需要的是實際的 loadports 配置**

### **正確的資料結構**：
```javascript
// ipcStatus API 返回的資料結構
{
  "ipc_status": [
    {
      "device_id": "DEVICE_001",
      "name": "Device Name",
      "loadports": {
        "1": {
          "port_id": "DEVICE_001_LP1",
          "port_no": 1,
          "dual_port": 0,
          "mode": "AUTO",
          "status": "NORMAL"
        },
        "2": {
          "port_id": "DEVICE_001_LP2", 
          "port_no": 2,
          "dual_port": 0,
          "mode": "MANUAL",
          "status": "NORMAL"
        }
        // 注意：可能只有 2 個 loadport，不一定是 4 個
      }
    }
  ]
}
```

## 🛠️ **正確的實現方案**

### **1. 獲取實際資料**
```javascript
const showModal = async (data) => {
  // 基本設備資訊
  editData.deviceId = data.device_id;
  // ...
  
  // 重置所有 Loadport 資料
  loadportData.port1.selected = false;
  loadportData.port1.port_id = "";
  // ...
  
  try {
    // 獲取詳細的 IPC 狀態資料
    const statusRes = await ipcStatus();
    const ipcStatusData = statusRes.data.ipc_status;
    
    // 尋找當前設備的詳細資訊
    const deviceDetail = ipcStatusData.find(device => device.device_id === data.device_id);
    
    if (deviceDetail && deviceDetail.loadports) {
      // 根據實際的 loadports 資料初始化
      Object.values(deviceDetail.loadports).forEach(loadport => {
        const portKey = `port${loadport.port_no}`;
        if (loadportData[portKey]) {
          loadportData[portKey].selected = true;  // 現有的才選中
          loadportData[portKey].port_id = loadport.port_id;  // 使用實際的 ID
          loadportData[portKey].port_no = loadport.port_no;
          loadportData[portKey].dual_port = loadport.dual_port || 0;
        }
      });
    }
  } catch (error) {
    // 錯誤處理，不影響 Modal 開啟
  }
};
```

### **2. 真實的業務邏輯**
- **✅ 只顯示實際存在的 Loadport**：如果設備只有 2 個 Loadport，就只選中 2 個
- **✅ 使用實際的 port_id**：不是假設的格式，而是從 API 獲取的真實值
- **✅ 用戶可以修改**：port_id 仍然可以編輯
- **✅ 用戶可以新增**：未選中的 Loadport 可以手動選中並配置

### **3. 用戶體驗**
```
編輯設備 "DEVICE_001" 時：

現有配置：
☑ Loadport 1: DEVICE_001_LP1    (已存在，預選中)
☑ Loadport 2: DEVICE_001_LPA    (已存在，預選中，用戶曾修改過)
☐ Loadport 3: [空白]            (不存在，未選中)
☐ Loadport 4: [空白]            (不存在，未選中)

用戶可以：
1. 修改現有的 port_id (如將 LP1 改為 LPA)
2. 取消選中某個 Loadport (刪除)
3. 選中並配置新的 Loadport (新增)
```

## 🧪 **測試場景**

### **場景 1：設備有 2 個 Loadport**
```
輸入：device_id = "DEV_001"
API 返回：loadports = {"1": {...}, "2": {...}}
預期結果：
- port1.selected = true, port1.port_id = "實際值"
- port2.selected = true, port2.port_id = "實際值"  
- port3.selected = false, port3.port_id = ""
- port4.selected = false, port4.port_id = ""
```

### **場景 2：設備有 4 個 Loadport**
```
輸入：device_id = "DEV_002"
API 返回：loadports = {"1": {...}, "2": {...}, "3": {...}, "4": {...}}
預期結果：
- 所有 4 個 port 都被選中，使用實際的 port_id
```

### **場景 3：設備沒有 Loadport**
```
輸入：device_id = "DEV_003"
API 返回：loadports = {} 或 null
預期結果：
- 所有 port 都未選中，port_id 為空
- 用戶可以手動配置
```

### **場景 4：API 獲取失敗**
```
輸入：任何 device_id
API 錯誤：網路錯誤或其他問題
預期結果：
- 所有 port 都未選中，port_id 為空
- Modal 仍然可以開啟
- 用戶可以手動配置
```

## 🎯 **關鍵改進**

1. **✅ 真實資料驅動**：不再假設，完全基於 API 返回的實際資料
2. **✅ 靈活配置**：支援任意數量的 Loadport (1-4 個)
3. **✅ 用戶可控**：保留編輯和選擇的靈活性
4. **✅ 錯誤容忍**：API 失敗不影響功能使用
5. **✅ 向後兼容**：不影響現有的 Add Modal 功能

## 📋 **API 調用順序**

1. **用戶點擊編輯** → 傳入基本設備資訊 (來自 ipcGetAll)
2. **Modal 開啟** → 調用 ipcStatus 獲取詳細資訊
3. **資料初始化** → 根據實際 loadports 設置選擇狀態
4. **用戶編輯** → 修改 port_id 或選擇狀態
5. **提交保存** → 發送修改後的 ports 陣列

這樣的實現完全符合用戶的需求：以實際獲取的資料為主，不假設，不預設全選。
