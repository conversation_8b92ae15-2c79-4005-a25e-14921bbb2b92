# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3 web application for Gyro Systems - a system management and monitoring interface. The project uses modern Vue ecosystem tools including Vite, Pinia, Vue Router, and Bootstrap Vue Next.

## Development Commands

### Setup and Development
```bash
# Install dependencies (requires Node.js 18+ and PNPM 8.6.10+)
pnpm install

# Start development server (runs on http://localhost:5173)
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview
```

### Code Quality
The project uses ESLint, Prettier, and <PERSON>lint with <PERSON><PERSON> pre-commit hooks:
```bash
# Linting is automatically run on commit via lint-staged
# Manual linting commands are not defined in package.json scripts
```

## Architecture

### Key Technologies
- **Framework**: Vue 3 with Composition API
- **Build Tool**: Vite with auto-imports for Vue, Vue Router, and Pinia
- **State Management**: Pinia stores in `src/store/modules/`
- **Routing**: Dynamic route loading from `src/router/modules/`
- **UI Framework**: Bootstrap Vue Next with custom SCSS themes
- **HTTP Client**: Axios configured in `src/apis/`
- **Internationalization**: Vue I18n with 4 locales (en_US, zh_TW, zh_CN, ja_JPN)

### Project Structure
- `src/views/` - Page components organized by feature areas (dashboard, account, etc.)
- `src/components/` - Reusable UI components and widgets
- `src/store/modules/` - Pinia store modules (auth, serverData, etc.)
- `src/router/modules/` - Route definitions auto-loaded by the router
- `src/apis/` - API configuration and endpoint definitions
- `src/assets/scss/` - SCSS styling with theme support
- `src/locales/` - Translation files for multi-language support

### Modal System
The application uses a sophisticated modal system with:
- `src/strategies/modalStrategies.js` - Strategy pattern for different modal types
- `src/composables/useModalData.js` - Composable for modal data management
- Modal components co-located with their parent views (e.g., `addModal.vue`, `editModal.vue`)

### Router Configuration
- Dynamic routing based on environment (`VITE_ROUTER_HISTORY`)
- Hash mode for production, history mode for development by default
- NProgress integration for route transitions
- Auto-loading of route modules from `src/router/modules/`

### Styling System
- Custom SCSS theme in `src/assets/scss/`
- Bootstrap 5 integration with Vue Next
- Component-specific styles organized by type (components, pages, plugins)
- Icon fonts and custom fonts in `src/assets/fonts/`

## Key Features
- Multi-dashboard system for different data types (IPC, logs, messages, servers)
- Tabulator.js integration for data tables
- Real-time data updates
- Comprehensive form validation with Vuelidate
- Chart visualization with ApexCharts integration
- Calendar functionality with FullCalendar
- Toast notifications and loading overlays