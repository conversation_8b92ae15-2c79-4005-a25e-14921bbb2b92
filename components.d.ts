/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CardHeader: typeof import('./src/components/card-header.vue')['default']
    ConfirmModal: typeof import('./src/components/ConfirmModal.vue')['default']
    DataModal: typeof import('./src/components/DataModal.vue')['default']
    Footer: typeof import('./src/components/footer.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    Loading: typeof import('./src/components/loading.vue')['default']
    Lottie: typeof import('./src/components/widgets/lottie.vue')['default']
    Menu: typeof import('./src/components/menu.vue')['default']
    NavBar: typeof import('./src/components/nav-bar.vue')['default']
    PageHeader: typeof import('./src/components/page-header.vue')['default']
    RightBar: typeof import('./src/components/right-bar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
