<script setup>
import { onMounted, onUnmounted, ref } from "vue";
import simplebar from "simplebar-vue";
import { useRouter } from "vue-router";

localStorage.setItem('hoverd', false);

const isMenuCondensed = ref(false);

document.documentElement.setAttribute("data-layout", "vertical");
document.documentElement.setAttribute("data-bs-theme", "light");
document.documentElement.setAttribute("data-preloader", "disable");
document.documentElement.setAttribute("data-layout-width", "fluid");
document.documentElement.setAttribute("data-layout-position", "fixed");
document.documentElement.setAttribute("data-topbar", "light");
document.documentElement.setAttribute("data-layout-style", "default");
document.documentElement.setAttribute("data-sidebar", "dark");
document.documentElement.setAttribute("data-sidebar-image", "none");

const router = useRouter();

/**
 * Vertical layout
 */
const toggleMenu = () => {
  document.body.classList.toggle("sidebar-enable");
      if (window.screen.width >= 992) {
        // eslint-disable-next-line no-unused-vars
        router.afterEach((routeTo, routeFrom) => {
          document.body.classList.remove("sidebar-enable");
          document.body.classList.remove("vertical-collpsed");
        });
        document.body.classList.toggle("vertical-collpsed");
      } else {
        // eslint-disable-next-line no-unused-vars
        router.afterEach((routeTo, routeFrom) => {
          document.body.classList.remove("sidebar-enable");
        });
        document.body.classList.remove("vertical-collpsed");
      }
  isMenuCondensed.value = !isMenuCondensed.value
}

const toggleRightSidebar = () => {
  console.log('toggleRightSidebar Called')
}

const updateSidebarSize = () => {
      let sidebarSize = ''
      // Check window.screen.width and update the data-sidebar-size attribute
      if (window.innerWidth < 1025) {
        sidebarSize = "sm";
      } else {
        sidebarSize = "lg"; // Reset sidebarSize if screen width is >= 1025
      }
      // Update the data-sidebar-size attribute of document.documentElement
      document.documentElement.setAttribute("data-sidebar-size", sidebarSize);
}

const initActiveMenu = () => {
  if (document.documentElement.getAttribute('data-sidebar-size') === 'sm-hover') {
        localStorage.setItem('hoverd', true);
        document.documentElement.setAttribute('data-sidebar-size', 'sm-hover-active');
      } else if (document.documentElement.getAttribute('data-sidebar-size') === 'sm-hover-active') {
        localStorage.setItem('hoverd', false);
        document.documentElement.setAttribute('data-sidebar-size', 'sm-hover');
      } else {
        document.documentElement.setAttribute('data-sidebar-size', 'sm-hover');
      }
}

onMounted(() => {
    if (localStorage.getItem('hoverd') == 'true') {
      document.documentElement.setAttribute('data-sidebar-size', 'sm-hover-active');
    }

    document.getElementById('overlay').addEventListener('click', () => {
      document.body.classList.remove('vertical-sidebar-enable');
    });

    if (window.screen.width < 1025) {
      document.documentElement.setAttribute("data-sidebar-size", "sm");
    }

    window.addEventListener("resize", () => {
      document.body.classList.remove('vertical-sidebar-enable');
      document.querySelector(".hamburger-icon").classList.add("open")
      updateSidebarSize()
    });
})

onUnmounted(() => {
  window.removeEventListener("resize", updateSidebarSize )
})
</script>
  
<template>
  <div id="layout-wrapper">
    <NavBar @toggleMenu="toggleMenu" @toggleRightSidebar="toggleRightSidebar" />
    <div>
      <!-- ========== Left Sidebar Start ========== -->
      <!-- ========== App Menu ========== -->
      <div class="app-menu navbar-menu">
        <!-- LOGO -->
        <div class="navbar-brand-box">
          <!-- Dark Logo-->
          <router-link to="/" class="logo logo-dark">
            <span class="logo-sm">
              <img src="@/assets/images/logo/sys-icon-light.svg" alt="" height="40" />
            </span>
            <span class="logo-lg">
              <img src="@/assets/images/logo/sys-logo-light.svg" alt="" height="40" />
            </span>
          </router-link>
          <!-- Light Logo-->
          <router-link to="/" class="logo logo-light">
            <span class="logo-sm">
              <img src="@/assets/images/logo/sys-icon-dark.svg" alt="" height="40" />
            </span>
            <span class="logo-lg">
              <img src="@/assets/images/logo/sys-logo-dark.svg" alt="" height="40" />
            </span>
          </router-link>
          <BButton size="sm" class="p-0 fs-20 header-item float-end btn-vertical-sm-hover"
            id="vertical-hover" @click="initActiveMenu">
            <i class="ri-record-circle-line"></i>
          </BButton>
        </div>

        <simplebar id="scrollbar" class="h-100" ref="scrollbar">
          <Menu></Menu>
        </simplebar>
        <div class="sidebar-background"></div>
      </div>
      <!-- Left Sidebar End -->
      <!-- Vertical Overlay-->
      <div class="vertical-overlay" id="overlay"></div>
    </div>
    <!-- ============================================================== -->
    <!-- Start Page Content here -->
    <!-- ============================================================== -->

    <div class="main-content">
      <div class="page-content">
        <!-- Start Content-->
        <BContainer fluid>
          <slot />
        </BContainer>
      </div>
      <Footer />
    </div>
    <!-- <RightBar /> -->
  </div>
</template>