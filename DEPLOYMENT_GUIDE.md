# Vue SPA 部署指南 - 解決重新整理白畫面問題

## 🔍 **問題說明**
當 Vue SPA 使用 History 模式時，在生產環境中重新整理頁面會導致 Web Server 嘗試尋找實際不存在的文件路徑，導致 404 錯誤或白畫面。

## 🛠️ **解決方案**

### **方案 1：使用 Hash 模式（最簡單，已配置）**

**配置狀態**：✅ 已完成
- 修改了 `.env.production` 文件設置 `VITE_ROUTER_HISTORY = "hash"`
- 增強了路由配置，支持智能模式選擇

**優點**：
- 無需 Web Server 配置
- 100% 兼容所有 Web Server
- 立即生效

**缺點**：
- URL 會包含 `#` 符號（如：`http://example.com/#/dashboard/main`）
- SEO 不友好

### **方案 2：Web Server 配置（推薦用於生產環境）**

#### **Apache 配置**
**文件位置**：`public/.htaccess` (已創建)
```apache
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
</IfModule>
```

#### **IIS 配置**
**文件位置**：`public/web.config` (已創建)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Handle History Mode and hash fallback" stopProcessing="true">
          <match url="(.*)" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

#### **Nginx 配置**
在 Nginx 配置文件中添加：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 靜態資源緩存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### **方案 3：智能路由模式（已實現）**

**配置狀態**：✅ 已完成
- 修改了 `src/router/index.js`，支持智能模式選擇
- 開發環境自動使用 History 模式
- 生產環境可通過環境變數控制

**智能選擇邏輯**：
1. 如果 `VITE_ROUTER_HISTORY = "hash"` → 使用 Hash 模式
2. 如果 `VITE_ROUTER_HISTORY = "history"` → 使用 History 模式
3. 如果未設置 → 開發環境用 History，生產環境用 Hash

## 🚀 **部署步驟**

### **立即解決方案（推薦）**
1. **重新構建項目**：
   ```bash
   npm run build
   ```

2. **部署到 Web Server**：
   - 將 `dist` 目錄內容上傳到 Web Server
   - 確保 `.htaccess` 或 `web.config` 文件也被上傳

3. **測試**：
   - 訪問任意頁面並重新整理
   - 確認不再出現白畫面

### **如果要使用 History 模式**
1. **修改環境變數**：
   ```env
   # .env.production
   VITE_ROUTER_HISTORY = "history"
   ```

2. **配置 Web Server**：
   - Apache：確保 `.htaccess` 文件在根目錄
   - IIS：確保 `web.config` 文件在根目錄
   - Nginx：添加相應的 server 配置

3. **重新構建和部署**

## 🧪 **測試清單**

- [ ] 首頁載入正常
- [ ] 直接訪問子路由（如 `/dashboard/main`）正常
- [ ] 在任意頁面重新整理不出現白畫面
- [ ] 瀏覽器前進/後退功能正常
- [ ] 靜態資源（CSS、JS、圖片）載入正常

## 📋 **故障排除**

### **仍然出現白畫面**
1. 檢查瀏覽器開發者工具的 Console 和 Network 標籤
2. 確認 Web Server 配置文件是否正確上傳
3. 檢查 Web Server 是否支持 URL Rewrite
4. 嘗試使用 Hash 模式作為備用方案

### **靜態資源 404**
1. 檢查 `VITE_BASE_URL` 配置是否正確
2. 確認資源路徑是否與部署路徑匹配
3. 檢查 Web Server 的靜態文件服務配置

## 🎯 **推薦配置**

**對於大多數情況，建議使用當前的 Hash 模式配置**：
- 簡單可靠，無需額外的 Web Server 配置
- 兼容性最好
- 立即生效

**如果需要 SEO 友好的 URL，可以考慮 History 模式 + Web Server 配置**。
