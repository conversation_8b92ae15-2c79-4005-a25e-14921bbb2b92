<script setup>
import { ref, onMounted, provide } from "vue";
import Layout from "@/layouts/main.vue";
import PageHeader from "@/components/page-header.vue";
import Tabulator from "./tabulator.vue";
import Loading from "@/components/Loading.vue";
import AddModal from "./addModal.vue";
import { getAccountAllData } from "@/apis/e84_Api";

let getAllData = ref(null);
let isLoading = ref(false);
let addModalShow = ref(false);
const modalIcon = ref("ri-alert-line");
const addModalFunc = ref(null);
const tabulatorFunc = ref(null);
const searchQuery = ref("");
const searchKeywords = ref("");

const getMsgData = async () => {
  try {
    isLoading.value = true;
    const res = await getAccountAllData();

    const dataList = res.data.users;
    console.log("Account All Data:", dataList);

    getAllData.value = dataList;
    isLoading.value = false;
  } catch (error) {
    console.log(error);
  }
};

const refreshData = () => {
  getMsgData();
};

const resetSearch = () => {
  searchQuery.value = "";
  searchKeywords.value = "";
  tabulatorFunc.value.searcher(searchQuery);
  refreshData();
};

provide('refreshData', refreshData)

const onKeyUpSearch = event => {
  if (event.key === 'Enter' && Object.values(searchQuery).filter(value => value !== '').length > 0) {
    searchKeywords.value = searchQuery.value;
    tabulatorFunc.value.searcher(searchQuery);    
  }
};

const showAddModal = () => {
  addModalFunc.value.showModal();
};

const updateValue = (value) => {
  searchQuery.value = value;
}

onMounted(() => {
  getMsgData();
});
</script>
<template>
  <Layout>
    <PageHeader title="Accounts Management" pageTitle="Main Dashboards" />
    <BCard no-body>
      <BCardHeader>
        <BRow class="g-4 align-items-center">
          <BCol sm="auto">
            <div class="d-flex align-items-center">
              <!-- <BCardTitle class="mb-0 flex-grow-1">{{ title }}</BCardTitle> -->
              <BButton type="button" variant="success" class="me-5" @click="showAddModal">
                <i class="ri-add-circle-line align-middle me-1"></i>
                Add
              </BButton>
              <div v-if="searchKeywords">
                <span class="badge bg-info-subtle text-info badge-border fs-6">Search Keyword: {{ searchKeywords }}</span>
              </div>
            </div>
          </BCol>
          <BCol sm>
            <div class="d-flex justify-content-sm-end">
              <BButton
                type="button"
                class="me-2"
                variant="success"
                v-throttle="refreshData"
                :delay="300"
              >
                <i class="ri-refresh-line align-middle me-1"></i>
                Refresh
              </BButton>
              <BButton
                type="button"
                class="me-2"
                variant="success"
                v-throttle="resetSearch"
                :delay="300"
              >
                Reset
              </BButton>
              <div class="search-box ms-2">
                <input
                  type="text"
                  class="form-control"
                  id="searchResultList"
                  placeholder="Search ..."
                  v-model="searchQuery"
                  @keyup="onKeyUpSearch"
                  on
                />
                <i class="ri-search-line search-icon"></i>
              </div>
            </div>
          </BCol>
        </BRow>
      </BCardHeader>
      <BCardBody>
        <div class="table-responsive table-card position-relative vl-parent">
          <Loading :is-loading="isLoading"></Loading>
          <Tabulator ref="tabulatorFunc" :ipcAllData="getAllData" :keyword="searchQuery" @update:value="updateValue" />
        </div>
      </BCardBody>
    </BCard>
    <AddModal ref="addModalFunc" :dataIcon="modalIcon" :modalShow="addModalShow" />
  </Layout>
</template>
<style>
.vl-overlay {
  z-index: 30 !important;
}

.vl-parent
  .tabulator
  .tabulator-header
  .tabulator-col
  .tabulator-col-content
  .tabulator-col-title {
  white-space: normal !important;
}

.vl-parent .tabulator .tabulator-header,
.tabulator .tabulator-headers,
.tabulator .tabulator-header .tabulator-col,
.tabulator .tabulator-header .tabulator-col .tabulator-col-content {
  height: 70px !important;
  display: inline-flex !important;
  align-items: center !important;
}

.vl-parent
  .tabulator
  .tabulator-header
  .tabulator-col
  .tabulator-col-content
  .tabulator-col-title-holder {
  display: contents;
}
</style>
