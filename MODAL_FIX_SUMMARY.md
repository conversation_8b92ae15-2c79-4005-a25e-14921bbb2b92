# Modal 表格資料消失問題修復總結

## 問題分析

在 Main Dashboard 的 Loadport 模式下，三個功能 Modal（Reset、Auto、Manual）存在以下問題：
- 第一次打開時表格資料正常顯示
- 關閉 Modal 後再次打開，表格資料消失

## 根本原因

1. **過度清理狀態**: `cleanup` 函數中的 `resetModalState()` 清空了所有 Modal 狀態，包括 `filteredData`
2. **時序問題**: Modal 關閉後狀態被完全重置，重新打開時無法正確恢復資料
3. **狀態管理邏輯缺陷**: `prepareModalData` 中的 `isVisible` 判斷邏輯不當

## 修復方案

### 1. 修改 DataModal.vue 的清理策略

**原始問題**:
```javascript
// 過度清理 - 清空所有狀態
if (immediate || props.tableMode === 'IPC') {
  resetModalState(); // 清空所有資料
} else {
  setTimeout(() => {
    resetModalState(); // 延遲也會清空所有資料
  }, 1000);
}
```

**修復後**:
```javascript
// 智能清理 - 只在必要時完全重置
if (immediate) {
  // 組件卸載時才完全重置狀態
  resetModalState();
} else {
  // Modal關閉時只清理選中項目和可見狀態，保留資料
  if (modalState.value) {
    modalState.value.selectedItems = [];
    modalState.value.isVisible = false;
    console.log('Modal state partially cleaned - data preserved');
  }
}
```

### 2. 改善 prepareModalData 邏輯

**新增重新打開檢測**:
```javascript
// 檢查是否是重新打開同一個Modal
const isReopeningModal = modalState.value.type === type && 
                         modalState.value.mode === tableMode && 
                         !modalState.value.isVisible;

// 如果是重新打開且有資料，保留現有的選中項目
const currentSelectedItems = isReopeningModal ? (modalState.value.selectedItems || []) : [];
```

### 3. 增強表格初始化和更新邏輯

**添加資料驗證和錯誤處理**:
```javascript
// 檢查是否有資料可以顯示
if (!modalState.value.filteredData || modalState.value.filteredData.length === 0) {
  console.warn('No filtered data available for table initialization');
  // 仍然創建表格，顯示空狀態
}

// 等待表格完全初始化
tabulator.value.on("tableBuilt", () => {
  console.log('Table built successfully with', modalState.value.filteredData?.length || 0, 'rows');
  
  // 如果有資料但表格為空，嘗試重新設置資料
  if (modalState.value.filteredData?.length > 0 && tabulator.value.getDataCount() === 0) {
    console.log('Table appears empty despite having data, attempting to reload...');
    setTimeout(() => {
      if (tabulator.value) {
        tabulator.value.setData(modalState.value.filteredData);
      }
    }, 100);
  }
});
```

### 4. 加強 Main Dashboard 的資料檢查

**在顯示 Modal 前驗證資料**:
```javascript
const showResetModal = () => {
  console.log('Reset button clicked, table mode:', tableMode.value);
  console.log('Raw data available for modal:', rawDataForModal.value?.length || 0, 'items');
  
  // 確保有資料才顯示Modal
  if (!rawDataForModal.value || rawDataForModal.value.length === 0) {
    console.warn('No data available for Reset modal');
    toast.warning('目前沒有可用的資料，請稍後再試');
    return;
  }
  
  resetModalShow.value = true;
};
```

## 修復效果

1. **保留資料狀態**: Modal 關閉時不再清空 `filteredData`，只清理選中項目
2. **智能重新打開**: 檢測到重新打開同一個 Modal 時，正確恢復資料狀態
3. **增強錯誤處理**: 添加詳細的日誌和錯誤處理，便於調試
4. **資料驗證**: 在顯示 Modal 前檢查資料可用性

## 測試建議

1. 在 Loadport 模式下測試三個 Modal 的打開/關閉循環
2. 檢查瀏覽器控制台的日誌輸出，確認資料流程正常
3. 驗證表格資料在重新打開時正確顯示
4. 測試不同的資料狀態（有資料/無資料）下的行為

## 新問題修復 - 部分選擇時選擇狀態丟失

### 🔍 **問題分析**
用戶反映在 Reset Modal 和 Manual Modal 中：
- 全選時功能正常
- 部分選擇（1-2筆資料）時，`modalState.value.selectedItems` 為空
- 導致驗證失敗，無法執行操作

### 🛠️ **根本原因**
1. **時序競爭問題**: `prepareModalData` 在用戶選擇過程中被重複調用
2. **狀態覆蓋**: 資料更新時會重置整個 `modalState`，清空選擇狀態
3. **同步延遲**: `rowSelectionChanged` 和狀態更新之間存在時序問題

### 🎯 **修復方案**

#### 1. 改進 `prepareModalData` 函數
```javascript
// 新增 preserveSelection 參數
const prepareModalData = (type, rawData, tableMode, preserveSelection = false) => {
  // 檢查是否是同一個Modal的資料更新
  const isSameModalUpdate = modalState.value.type === type &&
                            modalState.value.mode === tableMode &&
                            modalState.value.isVisible;

  // 智能選擇保留邏輯
  let selectedItems = [];
  if (preserveSelection && isSameModalUpdate) {
    selectedItems = modalState.value.selectedItems || [];
    console.log('Preserving current selection during data update');
  }
  // ...
}
```

#### 2. 修改資料更新邏輯
```javascript
const performDataUpdate = (newData, newDataHash) => {
  const selectedRows = tabulator.value ? tabulator.value.getSelectedRows().map(row => row.getData()) : [];
  const hasCurrentSelection = selectedRows.length > 0;

  // 如果有當前選擇，告訴 prepareModalData 保留選擇
  prepareModalData(props.type, newData, props.tableMode, hasCurrentSelection);
  // ...
}
```

#### 3. 增強選擇狀態同步
```javascript
config.rowSelectionChanged = (data) => {
  // 立即更新選擇狀態
  handleItemSelection(selectedRows);

  // 驗證更新後的狀態
  console.log('After handleItemSelection, modalState.selectedItems.length:', modalState.value.selectedItems.length);
}
```

#### 4. 添加確認時的狀態檢查
```javascript
const handleConfirm = async () => {
  // 檢查並同步選擇狀態 - 防止選擇狀態丟失
  if (tabulator.value) {
    const tabulatorSelected = tabulator.value.getSelectedRows().map(row => row.getData());

    // 如果 modalState 中的選擇為空，但 tabulator 中有選擇，同步過來
    if (modalState.value.selectedItems.length === 0 && tabulatorSelected.length > 0) {
      console.log('Syncing selection from tabulator to modalState');
      modalState.value.selectedItems = tabulatorSelected;
    }
  }
}
```

### 🧪 **測試重點**
1. **部分選擇測試**: 選擇 1-2 筆資料，確認能正常提交
2. **全選測試**: 確認全選功能仍然正常
3. **重複開關測試**: 多次打開/關閉 Modal，確認狀態正確
4. **資料更新測試**: 在選擇狀態下，確認資料更新不會清空選擇

## 注意事項

- 修改保持了向後兼容性
- 增加了詳細的日誌輸出，便於問題追蹤
- 保留了原有的功能邏輯，只修復了狀態管理問題
- 新增了多層保護機制，確保選擇狀態的可靠性
