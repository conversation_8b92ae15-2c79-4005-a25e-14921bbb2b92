<script setup>
import { ref, watch } from "vue";
import {
    useToast
} from "vue-toastification";
import { resetAllPorts } from "@/apis/e84_Api"; // 待後端 API 完成後取消註解

const props = defineProps(["dataShow"]);
const emit = defineEmits(["update:dataShow"]);
const toast = useToast();

const modalShow = ref(false);

// Watch for props changes
watch(() => props.dataShow, (newVal) => {
  console.log('resetModal props.dataShow changed:', newVal);
  modalShow.value = newVal;
  console.log('resetModal modalShow updated to:', modalShow.value);
}, { immediate: true });
const isLoading = ref(false);

const resetFunc = async () => {
    console.log("Reset All Ports Function Called");
    isLoading.value = true;
    
    try {
        // 待後端 API 完成後實作
        const res = await resetAllPorts();
        console.log("Reset All Ports:", res);
        
        // 模擬 API 呼叫
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        toast.success("Reset All Ports Success", {
            position: "bottom-right",
            timeout: 2000,
        });
        
        modalShow.value = false;
        emit('update:dataShow', false);
        
    } catch (error) {
        console.log("ERROR", error);
        toast.error(`ERROR: Reset All Ports Failed`, {
            position: "bottom-right",
            timeout: 2000,
        });
    } finally {
        isLoading.value = false;
    }
};

const closeModal = () => {
    console.log('resetModal closeModal called, emitting update:dataShow false');
    modalShow.value = false;
    emit('update:dataShow', false);
};

defineExpose({ modalShow });

</script>
<template>
  <BModal
    :model-value="modalShow"
    @update:model-value="(value) => { if (!value) closeModal(); }"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
    title="Reset All Ports"
  >
    <div class="modal-body text-center pt-0">
      <i class="ri-restart-line fs-c-10 text-danger-emphasis"></i>
      <div class="mt-3 pt-0">
        <h4>Are you sure to Reset All Device Ports?</h4>
        <p class="text-muted mt-2">This action will reset all device ports and cannot be undone.</p>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
        <BButton 
          type="button" 
          variant="danger" 
          class="me-3" 
          @click="resetFunc"
          :disabled="isLoading"
        >
          <span v-if="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ isLoading ? 'Resetting...' : 'Yes, Reset All' }}
        </BButton>
        <BButton 
          type="button" 
          variant="light" 
          @click="closeModal"
          :disabled="isLoading"
        >
          Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>