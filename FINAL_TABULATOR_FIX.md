# Tabulator 模式切換最終修復方案

## 🔍 **問題演進**

### **第一階段問題**：資料堆疊
- **原因**：`updateOrAddData` 無法匹配不同模式的索引字段
- **修復**：檢測模式變化，使用 `setData` 替換資料

### **第二階段問題**：模式切換無反應 + DOM 錯誤
- **原因**：重新初始化 Tabulator 導致 DOM 元素訪問錯誤
- **修復**：避免重新初始化，只更新列定義

### **第三階段問題**：資料堆疊回來了 + 分頁問題
- **原因**：不重新初始化導致 `index` 字段沒有更新，分頁狀態保持
- **現象**：
  - 切換到 IPC 模式：資料重複堆疊，頁數增加
  - 切回 Loadport 模式：頁數沒有重置

## 🛠️ **最終修復方案**

### **核心策略**：
**安全地重新初始化 Tabulator**，確保 `index` 字段正確更新，同時避免 DOM 錯誤

### **關鍵修復**：

#### **1. 安全的重新初始化流程**
```javascript
watch(() => props.mode, async (newMode, oldMode) => {
  // 1. 停止資料更新
  stopInterval()
  
  // 2. 安全地銷毀現有實例
  if (tableTabulator.value) {
    tableTabulator.value.destroy();
    tableTabulator.value = null;
  }
  
  // 3. 等待 DOM 穩定
  await nextTick();
  
  // 4. 檢查 DOM 元素存在
  if (tableRef.value) {
    tableTabulatorFunc();  // 重新初始化
  }
  
  // 5. 重啟資料更新
  refresh(newMode)
  startInterval();
});
```

#### **2. 改善初始化函數**
```javascript
const tableTabulatorFunc = () => {
  // 檢查 DOM 元素
  if (!tableRef.value) {
    console.error('Table DOM element not available');
    return;
  }
  
  // 安全銷毀
  if (tableTabulator.value) {
    tableTabulator.value.destroy();
    tableTabulator.value = null;
  }
  
  // 根據模式設置正確的 index
  const indexField = props.mode === 'Loadport' ? 'port_id' : 'device_id';
  
  // 創建新實例
  tableTabulator.value = new Tabulator(tableRef.value, {
    // ... 配置
    index: indexField,  // ✅ 正確的索引字段
    data: [],          // ✅ 初始化時不載入資料
    tableBuilt: function() {
      // 重置分頁到第一頁
      tableTabulator.value.setPage(1);
    }
  });
};
```

#### **3. 分頁重置**
```javascript
tableBuilt: function() {
  console.log('Tabulator built successfully for mode:', props.mode);
  // 表格建立後，重置分頁到第一頁
  if (tableTabulator.value) {
    tableTabulator.value.setPage(1);  // ✅ 重置分頁
  }
}
```

## 🎯 **修復效果**

### **解決的問題**：
1. **✅ 防止資料堆疊**：正確的 `index` 字段確保 `updateOrAddData` 能匹配資料
2. **✅ 模式切換正常**：安全的重新初始化流程避免 DOM 錯誤
3. **✅ 分頁重置**：模式切換時分頁回到第一頁
4. **✅ 狀態同步**：追蹤變數與實際狀態保持一致

### **工作流程**：
```
用戶點擊模式切換
    ↓
停止資料更新間隔
    ↓
安全銷毀現有 Tabulator
    ↓
等待 DOM 穩定 (nextTick)
    ↓
檢查 DOM 元素存在
    ↓
創建新 Tabulator (正確的 index)
    ↓
重置分頁到第一頁
    ↓
獲取新模式的資料
    ↓
重啟資料更新間隔
```

## 🧪 **測試場景**

### **場景 1：Loadport → IPC**
- **預期**：資料完全替換，不會堆疊，分頁重置到第一頁
- **檢查**：控制台顯示 "Creating Tabulator with index field: device_id"

### **場景 2：IPC → Loadport**
- **預期**：資料完全替換，分頁重置到第一頁
- **檢查**：控制台顯示 "Creating Tabulator with index field: port_id"

### **場景 3：多次切換**
- **預期**：每次切換都正常，沒有錯誤，分頁都重置

### **場景 4：每秒更新**
- **預期**：同模式下使用 `updateOrAddData` 增量更新，不會堆疊

## 📋 **關鍵改進**

1. **✅ 安全重新初始化**：使用 `nextTick` 確保 DOM 穩定
2. **✅ 正確索引字段**：每次重建都使用正確的 `index` 配置
3. **✅ 分頁重置**：模式切換時自動重置到第一頁
4. **✅ 錯誤處理**：完整的錯誤處理和日誌記錄
5. **✅ 狀態同步**：追蹤變數與實際狀態保持一致

這個最終方案平衡了功能需求和穩定性，確保模式切換既正確又穩定。
