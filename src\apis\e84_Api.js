import axios from "axios";
import {
    useToast
} from "vue-toastification";
import { getCookieToken } from '@/utils/cookies'

const toast = useToast();

const getConfig = JSON.parse(localStorage.getItem("serverConfig")) || [];
// console.log("CHECK CONFIG E84: ", getConfig);

const instance = axios.create({
    baseURL: `http://${getConfig.E84.ip_address}`,
    timeout: 15000,
    headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
    }
  })
  
  instance.interceptors.request.use(
    (config) => {
      const token = getCookieToken() || null
      // console.log("TOKEN: ", token)
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
        // config.headers.Authorization = token
      }
      return config
    },
    (error) => Promise.reject(error)
  )
  
  instance.interceptors.response.use(
    (res) => {
      return res
    },
    (error) => Promise.reject(error.response)
  )

export const E84_Request = async (url, method = 'get', data = null) => {
    const axiosInstance = await instance;
    axiosInstance.interceptors.response.use(response => {
        if (response.status < 400) {
            // console.log("Instance RESPONSE < 400：", response)
            return Promise.resolve(response);
        }
        else {
            // console.log("Instance RESPONSE > 400：", response)
            return Promise.reject(response)
        }
    }, error => {
        console.log("ERROR CHECK", error)
        if (error.response) {
            // console.log("ERROR", error)
            // console.log("GET API ERROR", error.response)
            toast.error(`ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]` , {
            position: "bottom-right",
            timeout: 1000,
            });
            return Promise.reject(error.response);
        } else {
            return Promise.reject(error);
        }
    })
    try {
        const response = await axiosInstance.request({
            url,
            method,
            data
        });
        return response;
    } catch (error) {
        console.error("Api Js Catch Error", error);
        return Promise.reject(error)
    }
}

// Login
export const authLogout = async () => E84_Request(`/api/auth/logout`, 'get');
export const authLogin = async (data) => E84_Request(`/api/auth/login`, 'post', data);


// IPC
export const ipcStatus = async () => E84_Request(`/api/ipc/status`, 'get');
export const ipcDeviceStatus = async () => E84_Request(`/api/ipc/status/device`, 'get');
export const ipcResetPort = async (data) => E84_Request(`/api/ipc/port/reset`, 'post', data);
export const ipcGetAll = async () => E84_Request(`/api/ipc/all`, 'get');
export const addIpcDevice = async (data) => E84_Request(`/api/ipc/`, 'post', data);
export const editIpcDevice = async (data) => E84_Request(`/api/ipc/`, 'put', data);
export const deleteIpcDevice = async (data) => E84_Request(`/api/ipc/`, 'delete', data);

// 批量操作 API
export const resetBatchByPortId = async (data) => E84_Request(`/api/ipc/port/reset_batch_by_port_id`, 'post', data);
export const automodeBatchByPortId = async (data) => E84_Request(`/api/ipc/port/automode_batch_by_port_id`, 'post', data);
export const automodeBatchByDeviceId = async (data) => E84_Request(`/api/ipc/port/automode_batch_by_device_id`, 'post', data);
export const resetBatchByDeviceId = async (data) => E84_Request(`/api/ipc/port/reset_batch_by_device_id`, 'post', data);
// Manual Mode 使用與 Auto Mode 相同的 API，但 Enable 設為 false
export const manualmodeBatchByPortId = async (data) => E84_Request(`/api/ipc/port/automode_batch_by_port_id`, 'post', data);
export const manualmodeBatchByDeviceId = async (data) => E84_Request(`/api/ipc/port/automode_batch_by_device_id`, 'post', data);

// Server

export const getServerList = async () => E84_Request(`/api/server/svr`, 'get');
export const editServerData = async (data) => E84_Request(`/api/server/svr`, 'put', data);

// Message
export const getMessageData = async (data) => E84_Request(`/api/message/list`, 'post', data);
export const updateMessageData = async (data) => E84_Request(`/api/message/server`, 'post', data);

export const getMessageAllData = async () => E84_Request(`/api/message/all`, 'get');

export const addMessageData = async (data) => E84_Request(`/api/message/`, 'post', data);
export const editMessageData = async (data) => E84_Request(`/api/message/`, 'put', data);
export const deleteMessageData = async (data) => E84_Request(`/api/message/?id=${data}`, 'delete');

// Logs

export const getLogList = async (data) => E84_Request(`/api/log/list`, 'post', data);
export const getAlarmList = async (data) => E84_Request(`/api/alarm/list`, 'post', data);
export const getEventList = async (data) => E84_Request(`/api/event/list`, 'post', data);

export const getIpcList = async (data) => E84_Request(`/api/ipc/select`, 'post', data);
export const getIpcFileList = async (data) => E84_Request(`/api/log/files/list`, 'post', data);
export const getIpcFileDownload = async (data) => E84_Request(`/api/log/files/download`, 'post', data);


// User Accounts
export const getAccountData = async (data) => E84_Request(`/api/user/list`, 'post', data);
export const updateAccountData = async (data) => E84_Request(`/api/user/server`, 'post', data);

export const getAccountAllData = async () => E84_Request(`/api/user/all`, 'get');

export const addAccountData = async (data) => E84_Request(`/api/user/`, 'post', data);
export const editAccountData = async (data) => E84_Request(`/api/user/`, 'put', data);
export const deleteAccountData = async (data) => E84_Request(`/api/user/`, 'delete', data);
