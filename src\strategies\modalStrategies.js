/**
 * Modal 策略模式實現
 * 定義不同類型 Modal 的配置和行為
 */

import { tabulatorModeFormatter } from "@/utils/modeFormatter";

/**
 * 基礎 Modal 策略類
 */
class BaseModalStrategy {
  constructor(type, tableMode) {
    this.type = type;
    this.tableMode = tableMode;
  }

  /**
   * 獲取 Modal 配置
   * @returns {Object} Modal 配置物件
   */
  getConfig() {
    throw new Error('Must implement getConfig method');
  }

  /**
   * 獲取表格配置
   * @returns {Object} 表格配置物件
   */
  getTableConfig() {
    throw new Error('Must implement getTableConfig method');
  }

  /**
   * 獲取操作按鈕配置
   * @returns {Array} 按鈕配置陣列
   */
  getActionButtons() {
    throw new Error('Must implement getActionButtons method');
  }

  /**
   * 驗證操作
   * @returns {Object} 驗證結果
   */
  validateOperation() {
    return {
      isValid: true,
      message: ''
    };
  }
}

/**
 * Reset Modal 策略
 */
class ResetModalStrategy extends BaseModalStrategy {
  getConfig() {
    return {
      title: `Reset Mode - ${this.tableMode}`,
      size: 'xl',
      showTable: true,
      showSelection: true,
      showStats: true,
      confirmRequired: true,
      dangerAction: true,
      description: 'Select devices to reset. This action will reset selected device ports and cannot be undone.',
      icon: 'ri-restart-line',
      iconClass: 'text-danger-emphasis'
    };
  }

  getTableConfig() {
    return {
      selectable: true,
      selectableCheck: () => {
        // 所有項目都可選擇
        return true;
      },
      height: '400px',
      pagination: true,
      pageSize: 10,
      showHeader: true,
      columns: this.getColumns()
    };
  }

  getColumns() {
    const baseColumns = this.getBaseColumns();
    
    // 只有在可選擇的模態框中才添加 Action 欄位
    if (this.getConfig().showSelection) {
      return [
        {
          title: '',
          field: 'select',
          width: 50,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: false,
          headerSort: false,
          formatter: 'rowSelection'
        },
        ...baseColumns
      ];
    }
    
    return baseColumns;
  }

  getBaseColumns() {
    if (this.tableMode === 'Loadport') {
      return [
        { 
          title: 'Device ID', 
          field: 'device_id', 
          minWidth: 100, 
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Device Name', 
          field: 'name', 
          minWidth: 120, 
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Port No', 
          field: 'port_no', 
          minWidth: 80, 
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Port ID', 
          field: 'port_id', 
          minWidth: 90, 
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Current Mode', 
          field: 'mode', 
          minWidth: 140,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true,
          formatter: tabulatorModeFormatter
        },
        { 
          title: 'Port State', 
          field: 'port_state', 
          minWidth: 100,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Status', 
          field: 'status', 
          minWidth: 90,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true,
          formatter: (cell) => {
            const value = cell.getValue();
            const statusClass = value === 'Online' ? 'text-success' : 'text-danger';
            return `<span class="${statusClass}">${value}</span>`;
          }
        },
        { 
          title: 'Detail', 
          field: 'detail', 
          minWidth: 120,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        }
      ];
    } else {
      // IPC 模式的欄位
      return [
        { 
          title: 'Device ID', 
          field: 'device_id', 
          minWidth: 100,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Name', 
          field: 'name', 
          minWidth: 120,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Group', 
          field: 'group', 
          minWidth: 90,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'IP', 
          field: 'ip', 
          minWidth: 120,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Port', 
          field: 'port', 
          minWidth: 70,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'IPC Enable', 
          field: 'ipc_enable', 
          minWidth: 100,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true,
          formatter: (cell) => {
            const value = cell.getValue();
            const badgeClass = value ? 'bg-success' : 'bg-secondary';
            const text = value ? 'Enabled' : 'Disabled';
            return `<span class="badge ${badgeClass}">${text}</span>`;
          }
        },
      ];
    }
  }

  getActionButtons() {
    return [
      {
        text: 'Select All',
        variant: 'outline-primary',
        action: 'selectAll',
        icon: 'ri-checkbox-multiple-line'
      },
      {
        text: 'Reset Selected',
        variant: 'danger',
        action: 'confirm',
        icon: 'ri-restart-line',
        loading: true,
        requireSelection: false
      },
      {
        text: 'Cancel',
        variant: 'light',
        action: 'cancel'
      }
    ];
  }

  validateOperation(selectedItems) {
    if (!selectedItems || selectedItems.length === 0) {
      const itemType = this.tableMode === 'Loadport' ? 'port' : 'device';
      return {
        isValid: false,
        message: `Please select at least one ${itemType} to reset`
      };
    }

    return {
      isValid: true,
      message: `Ready to reset ${selectedItems.length} ${this.tableMode === 'Loadport' ? 'port(s)' : 'device(s)'}`
    };
  }
}

/**
 * Auto Modal 策略
 */
class AutoModalStrategy extends BaseModalStrategy {
  getConfig() {
    return {
      title: `Auto Mode - ${this.tableMode}`,
      size: 'xl',
      showTable: true,
      showSelection: true,
      showStats: true,
      confirmRequired: true,
      dangerAction: false,
      description: 'Select devices to enable Auto mode. Only devices currently in Manual mode are shown.',
      icon: 'ri-play-circle-line',
      iconClass: 'text-success'
    };
  }

  getTableConfig() {
    return {
      selectable: true,
      selectableCheck: () => {
        // 所有項目都可選擇
        return true;
      },
      height: '400px',
      pagination: true,
      pageSize: 10,
      showHeader: true,
      columns: this.getColumns()
    };
  }

  getColumns() {
    const baseColumns = this.getBaseColumns();
    
    // 只有在可選擇的模態框中才添加 Action 欄位
    if (this.getConfig().showSelection) {
      return [
        {
          title: '',
          field: 'select',
          width: 50,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: false,
          headerSort: false,
          formatter: 'rowSelection'
        },
        ...baseColumns
      ];
    }
    
    return baseColumns;
  }

  getBaseColumns() {
    if (this.tableMode === 'Loadport') {
      return [
        { 
          title: 'Device ID', 
          field: 'device_id', 
          minWidth: 100, 
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Device Name', 
          field: 'name', 
          minWidth: 120, 
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Port No', 
          field: 'port_no', 
          minWidth: 80, 
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Port ID', 
          field: 'port_id', 
          minWidth: 90, 
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Current Mode', 
          field: 'mode', 
          minWidth: 140,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true,
          formatter: tabulatorModeFormatter
        },
        { 
          title: 'Port State', 
          field: 'port_state', 
          minWidth: 100,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Status', 
          field: 'status', 
          minWidth: 90,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true,
          formatter: (cell) => {
            const value = cell.getValue();
            const statusClass = value === 'Online' ? 'text-success' : 'text-danger';
            return `<span class="${statusClass}">${value}</span>`;
          }
        },
        { 
          title: 'Detail', 
          field: 'detail', 
          minWidth: 120,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        }
      ];
    } else {
      // IPC 模式的欄位
      return [
        { 
          title: 'Device ID', 
          field: 'device_id', 
          minWidth: 100,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Name', 
          field: 'name', 
          minWidth: 120,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Group', 
          field: 'group', 
          minWidth: 90,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'IP', 
          field: 'ip', 
          minWidth: 120,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Port', 
          field: 'port', 
          minWidth: 70,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true
        },
        { 
          title: 'Current Mode', 
          field: 'mode', 
          minWidth: 140,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true,
          formatter: tabulatorModeFormatter
        },
        { 
          title: 'IPC Enable', 
          field: 'ipc_enable', 
          minWidth: 100,
          hozAlign: 'center',
          headerHozAlign: 'center',
          resizable: true,
          formatter: (cell) => {
            const value = cell.getValue();
            const badgeClass = value ? 'bg-success' : 'bg-secondary';
            const text = value ? 'Enabled' : 'Disabled';
            return `<span class="badge ${badgeClass}">${text}</span>`;
          }
        }
      ];
    }
  }

  getActionButtons() {
    return [
      {
        text: 'Select All',
        variant: 'outline-primary',
        action: 'selectAll',
        icon: 'ri-checkbox-multiple-line'
      },
      {
        text: 'Apply to Selected',
        variant: 'success',
        action: 'confirm',
        icon: 'ri-play-circle-line',
        loading: true,
        requireSelection: false
      },
      {
        text: 'Cancel',
        variant: 'light',
        action: 'cancel'
      }
    ];
  }

  validateOperation(selectedItems) {
    // 允許空選擇，驗證邏輯移至父組件
    if (!selectedItems || selectedItems.length === 0) {
      return {
        isValid: true,
        message: 'No items selected for Auto mode operation'
      };
    }

    if (this.tableMode === 'Loadport') {
      // 由於 Auto Modal 只顯示 Manual 模式 (mode === 1) 的項目
      // 所以不需要檢查是否已經是 Auto 模式
      return {
        isValid: true,
        message: `Ready to enable Auto mode for ${selectedItems.length} port(s)`
      };
    } else {
      // IPC 模式不需要檢查 mode，因為是設備級別的操作
      return {
        isValid: true,
        message: `Ready to enable Auto mode for ${selectedItems.length} device(s)`
      };
    }
  }
}

/**
 * Manual Modal 策略
 */
class ManualModalStrategy extends BaseModalStrategy {
  getConfig() {
    return {
      title: `Manual Mode - ${this.tableMode}`,
      size: 'xl',
      showTable: true,
      showSelection: true,
      showStats: true,
      confirmRequired: true,
      dangerAction: false,
      description: 'Select devices to enable Manual mode. Only devices currently in Auto mode are shown.',
      icon: 'ri-hand-coin-line',
      iconClass: 'text-primary'
    };
  }

  getTableConfig() {
    return {
      selectable: true,
      selectableCheck: () => {
        // 所有項目都可選擇
        return true;
      },
      height: '400px',
      pagination: true,
      pageSize: 10,
      showHeader: true,
      columns: this.getColumns()
    };
  }

  getColumns() {
    // 與 AutoModalStrategy 相同的欄位配置
    return new AutoModalStrategy(this.type, this.tableMode).getColumns();
  }

  getActionButtons() {
    return [
      {
        text: 'Select All',
        variant: 'outline-primary',
        action: 'selectAll',
        icon: 'ri-checkbox-multiple-line'
      },
      {
        text: 'Apply Manual Mode',
        variant: 'primary',
        action: 'confirm',
        icon: 'ri-hand-coin-line',
        loading: true,
        requireSelection: false
      },
      {
        text: 'Cancel',
        variant: 'light',
        action: 'cancel'
      }
    ];
  }

  validateOperation(selectedItems) {
    if (!selectedItems || selectedItems.length === 0) {
      const itemType = this.tableMode === 'Loadport' ? 'port' : 'device';
      return {
        isValid: false,
        message: `Please select at least one ${itemType} to enable Manual mode`
      };
    }

    if (this.tableMode === 'Loadport') {
      // 由於 Manual Modal 只顯示 Auto 模式 (mode === 2) 的項目
      // 所以不需要檢查是否已經是 Manual 模式
      return {
        isValid: true,
        message: `Ready to enable Manual mode for ${selectedItems.length} port(s)`
      };
    } else {
      // IPC 模式不需要檢查 mode，因為是設備級別的操作
      return {
        isValid: true,
        message: `Ready to enable Manual mode for ${selectedItems.length} device(s)`
      };
    }
  }
}

/**
 * 策略工廠函數
 * @param {string} type - Modal 類型 ('reset', 'auto', 'manual')
 * @param {string} tableMode - 表格模式 ('Loadport', 'IPC')
 * @returns {BaseModalStrategy} 對應的策略實例
 */
export function createModalStrategy(type, tableMode) {
  const strategies = {
    reset: ResetModalStrategy,
    auto: AutoModalStrategy,
    manual: ManualModalStrategy
  };

  const StrategyClass = strategies[type];
  if (!StrategyClass) {
    throw new Error(`Unknown modal strategy type: ${type}`);
  }

  return new StrategyClass(type, tableMode);
}

/**
 * 獲取所有可用的策略類型
 * @returns {Array} 策略類型陣列
 */
export function getAvailableStrategies() {
  return ['reset', 'auto', 'manual'];
}

/**
 * 驗證策略類型
 * @param {string} type - 策略類型
 * @returns {boolean} 是否為有效類型
 */
export function isValidStrategyType(type) {
  return getAvailableStrategies().includes(type);
}