<script setup>
import { ref, watch } from "vue";
import {
    useToast
} from "vue-toastification";
import { setAutoMode } from "@/apis/e84_Api"; // 待後端 API 完成後取消註解

const props = defineProps(["dataShow"]);
const emit = defineEmits(["update:dataShow"]);
const toast = useToast();

const modalShow = ref(false);

// Watch for props changes
watch(() => props.dataShow, (newVal) => {
  console.log('autoModal props.dataShow changed:', newVal);
  modalShow.value = newVal;
  console.log('autoModal modalShow updated to:', modalShow.value);
}, { immediate: true });
const isLoading = ref(false);

const autoFunc = async () => {
    console.log("Auto Mode Function Called");
    isLoading.value = true;
    
    try {
        // 待後端 API 完成後實作
        const res = await setAutoMode();
        console.log("Set Auto Mode:", res);
        
        // 模擬 API 呼叫
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        toast.success("Auto Mode Enabled Successfully", {
            position: "bottom-right",
            timeout: 2000,
        });
        
        modalShow.value = false;
        emit('update:dataShow', false);
        
    } catch (error) {
        console.log("ERROR", error);
        toast.error(`ERROR: Auto Mode Setup Failed`, {
            position: "bottom-right",
            timeout: 2000,
        });
    } finally {
        isLoading.value = false;
    }
};

const closeModal = () => {
    console.log('autoModal closeModal called, emitting update:dataShow false');
    modalShow.value = false;
    emit('update:dataShow', false);
};

defineExpose({ modalShow });

</script>
<template>
  <BModal
    :model-value="modalShow"
    @update:model-value="(value) => { if (!value) closeModal(); }"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
    title="Enable Auto Mode"
  >
    <div class="modal-body text-center pt-0">
      <i class="ri-play-circle-line fs-c-10 text-success-emphasis"></i>
      <div class="mt-3 pt-0">
        <h4>Enable Auto Mode for All Devices?</h4>
        <p class="text-muted mt-2">This will automatically manage all device operations based on predefined settings.</p>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
        <BButton 
          type="button" 
          variant="success" 
          class="me-3" 
          @click="autoFunc"
          :disabled="isLoading"
        >
          <span v-if="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ isLoading ? 'Enabling...' : 'Yes, Enable Auto' }}
        </BButton>
        <BButton 
          type="button" 
          variant="light" 
          @click="closeModal"
          :disabled="isLoading"
        >
          Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>