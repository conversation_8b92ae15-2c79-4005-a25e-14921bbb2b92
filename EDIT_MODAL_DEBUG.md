# Edit Modal 參數不送出問題分析與修復

## 🔍 **問題分析**

### **發現的問題**：
1. **computed 屬性邏輯錯誤**：原本的 `computedPort1` 等屬性被修改成條件式，當 checkbox 未選中時返回空字串
2. **showModal 初始化邏輯問題**：嘗試從不存在的 `data.loadports` 屬性初始化選擇狀態
3. **資料流問題**：Edit Modal 接收的資料結構與預期不符

### **根本原因**：
- Edit Modal 的資料來源是 IPC 設備列表，不包含 `loadports` 屬性
- computed 屬性的條件邏輯導致未選中的 port 產生空的 `port_id`
- 初始化邏輯不正確，導致選擇狀態異常

## 🛠️ **修復方案**

### **1. 修復 computed 屬性**
```javascript
// 修復前（錯誤）
const computedPort1 = computed(() => {
  return loadportSelection.port1 ? `${editData.deviceId}_LP1` : ''  // ❌ 會產生空字串
});

// 修復後（正確）
const computedPort1 = computed(() => {
  return `${editData.deviceId}_LP1`  // ✅ 始終產生正確的 port_id
});
```

### **2. 修復 showModal 初始化邏輯**
```javascript
// 修復前（錯誤）
if (data.loadports) {
  loadportSelection.port1 = !!data.loadports['1'];  // ❌ data.loadports 不存在
} else {
  loadportSelection.port1 = false;  // ❌ 預設為未選中
}

// 修復後（正確）
// 對於編輯模式，預設所有 Loadport 都選中
loadportSelection.port1 = true;  // ✅ 編輯模式預設全選
loadportSelection.port2 = true;
loadportSelection.port3 = true;
loadportSelection.port4 = true;
```

### **3. API 請求邏輯保持正確**
```javascript
// 這部分邏輯是正確的
const selectedPorts = [];

if (loadportSelection.port1) {
  selectedPorts.push({
    port_id: computedPort1.value,  // 現在會正確產生 port_id
    port_no: 1,
    dual_port: 0
  });
}
// ... 其他 port 的處理
```

## 🧪 **測試步驟**

### **1. 基本功能測試**
1. 打開任一 IPC 設備的 Edit Modal
2. 檢查 4 個 Loadport checkbox 是否都預設選中
3. 檢查 Loadport 輸入框是否顯示正確的 port_id（如：`DEVICE_ID_LP1`）

### **2. 選擇功能測試**
1. 取消選中某些 Loadport checkbox
2. 檢查對應的輸入框是否被禁用
3. 檢查 "x/4 Selected" 計數是否正確

### **3. API 請求測試**
1. 選中部分 Loadport（如只選 1 和 2）
2. 點擊 Save 按鈕
3. 檢查瀏覽器 Network 標籤中的請求內容
4. 確認 `ports` 陣列只包含選中的 Loadport

### **4. 驗證測試**
1. 取消選中所有 Loadport
2. 點擊 Save 按鈕
3. 應該顯示警告訊息："Please select at least one Loadport"

## 📋 **預期結果**

### **正確的 API 請求格式**：
```json
{
  "id": "device_id",
  "device_id": "TEST_DEVICE",
  "name": "Test Device",
  "group": "Group1",
  "ip": "*************",
  "port": "8080",
  "ipc_enable": true,
  "ports": [
    {
      "port_id": "TEST_DEVICE_LP1",
      "port_no": 1,
      "dual_port": 0
    },
    {
      "port_id": "TEST_DEVICE_LP2",
      "port_no": 2,
      "dual_port": 0
    }
  ]
}
```

### **控制台日誌**：
```
Edit Modal - Received data: {id: "...", device_id: "...", ...}
Edit Modal - Loadport selection initialized: {port1: true, port2: true, port3: true, port4: true}
Edit Device: null
Loadport Selection: {port1: true, port2: false, port3: false, port4: false}
Selected Ports: [{port_id: "DEVICE_ID_LP1", port_no: 1, dual_port: 0}]
EDIT DATA: {id: "...", device_id: "...", ..., ports: [...]}
```

## 🎯 **關鍵修復點**

1. **✅ computed 屬性恢復正常**：始終返回正確的 port_id
2. **✅ 初始化邏輯修正**：編輯模式預設全選
3. **✅ 添加調試日誌**：便於追蹤問題
4. **✅ 保持 API 邏輯不變**：只發送選中的 ports

現在 Edit Modal 的 Loadport 選擇功能應該可以正常工作，參數會正確送出到 API。
