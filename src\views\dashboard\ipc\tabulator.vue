<script setup>
import { onMounted, onBeforeUnmount, onBeforeUpdate, ref, watch } from "vue";
import { TabulatorFull as Tabulator } from "tabulator-tables";
import EditModal from "./editModal.vue";
import DeleteModal from "./deleteModal.vue";
import dayjs from 'dayjs'

const formatDate = (isoString) => {
  return dayjs(isoString).format('YYYY/MM/DD HH:mm:ss')
}

const props = defineProps(["ipcAllData", "keyword"]);

let tableData = ref([]);
let tableRef = ref(null);
const tableTabulator = ref(null);
let selectedData = [];
let editModalShow = ref(false);
let deleteModalShow = ref(false);
const modalIcon = ref("ri-alert-line");
const editModalFunc = ref(null);
const deleteModalFunc = ref(null);

let searchStatus = ref(false);
let searchValue = ref({
  value: "",
  result: "",
  postStatus: "",
});

const tableTabulatorFunc = () => {
  try {
    tableTabulator.value = new Tabulator(tableRef.value, {
      placeholder: "No Data Available",
      data: tableData.value,
      pagination: "remote",
      paginationSize: 10,
      rowHeight: 50,
      paginationSizeSelector: [10, 25, 50, 100],
      paginationCounter: "rows",
      layout: "fitColumns", //fitData, fitDataFill, fitDataTable, fitDataStretch, fitColumns
      autoResize: false,
      reactiveData: true,
      dataLoader: true,
      height: 644,
      progressiveLoadDelay: 200,
      columns: [
        {
          title: "Option",
          field: "",
          headerHozAlign: "center",
          hozAlign: "center",
          headerSort: false,
          width: 150,
          
          formatter: function (cell) {
            const groupDiv = document.createElement("div");
            groupDiv.classList.add(
              "btn-group",
              "d-flex",
              "align-items-stretch",
              "justify-content-between",
              "h-100",
              "p-1"
            );

            const button = document.createElement("button");
            button.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="me-1" style="height:20px;"><path stroke-linecap="round" stroke-linejoin="round" d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z"/></svg> Options`;

            button.classList.add(
              "btn",
              "btn-soft-dark",
              "btn-block",
              "waves-effect",
              "waves-light",
              "dropdown-toggle",
              "d-flex",
              "align-items-center",
              "justify-content-center"
            );

            button.setAttribute("type", "button");
            button.setAttribute("data-bs-toggle", "dropdown");
            button.setAttribute("aria-expanded", "false");

            const ul = document.createElement("ul");
            ul.classList.add("dropdown-menu", "border", "border-white", "py-0");
            ul.setAttribute("style", "min-width: 95%");

            const editLi = document.createElement("li");
            const editLink = document.createElement("div");
            editLink.textContent = "Edit";
            editLink.classList.add(
              "py-0",
              "bg-primary",
              "text-white",
              "fw-bold",
              "text-center",
              "text-uppercase",
              "cursor-pointer"
            );

            editLi.appendChild(editLink);
            editLink.addEventListener("click", function (event) {
              event.stopPropagation();
              // showEditModal(cell.getData());
              console.log("Edit Clicked");
              ul.classList.remove("show");
              editModalFunc.value.showModal(cell.getData());
            });

            const deleteLi = document.createElement("li");
            const deleteLink = document.createElement("div");
            deleteLink.textContent = "Delete";
            deleteLink.classList.add(
              "py-0",
              "bg-danger",
              "text-white",
              "fw-bold",
              "text-center",
              "text-uppercase",
              "cursor-pointer"
            );

            deleteLi.appendChild(deleteLink);
            deleteLink.addEventListener("click", function (event) {
              event.stopPropagation();
              // showEditModal(cell.getData());
              console.log("Delete Clicked");
              ul.classList.remove("show");
              deleteModalFunc.value.showModal(cell.getData().device_id);
            });

            ul.appendChild(editLi);
            ul.appendChild(deleteLi);

            groupDiv.appendChild(button);
            groupDiv.appendChild(ul);

            const outerDiv = document.createElement("div");

            outerDiv.appendChild(groupDiv);

            return groupDiv;
          },
        },
        {
          title: "Device ID",
          field: "device_id",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 120,
          
        },
        {
          title: "Name",
          field: "name",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 120,
        },
        {
          title: "Group",
          field: "group",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 120,
        },
        {
          title: "IP",
          field: "ip",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
        },
        {
          title: "Port",
          field: "port",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 80,
        },
        {
          title: "Port ID",
          field: "port_id",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
        },
        {
          title: "Port No.",
          field: "port_no",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 100,
        },
        // {
        //   title: "Dual Port",
        //   field: "dual_port",
        //   headerHozAlign: "center",
        //   hozAlign: "center",
        //   vertAlign : "middle",
        //   width: 120,
        // },
        {
          title: "IPC Enable",
          field: "ipc_enable",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 120,          
          formatter: function (cell) {
            return cell.getValue() == true ? `<span class="badge text-bg-success rounded-pill text-uppercase">${ cell.getValue() }</span>` : `<span class="badge text-bg-danger rounded-pill text-uppercase">${ cell.getValue() }</span>`;
          },
        },
        // {
        //   title: "FTP Enable",
        //   field: "ftp_enable",
        //   headerHozAlign: "center",
        //   hozAlign: "center",
        //   vertAlign : "middle",
        //   width: 120,
        //   formatter: function (cell) {
        //     return cell.getValue() == true ? `<span class="badge text-bg-success rounded-pill text-uppercase">${ cell.getValue() }</span>` : `<span class="badge text-bg-danger rounded-pill text-uppercase">${ cell.getValue() }</span>`;
        //   },
        // },
        {
          title: "Created At",
          field: "ipc_created_at",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 300,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${formatDate(cell.getValue())}</span>`;
          },
        },
        {
          title: "Updated At",
          field: "ipc_updated_at",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 300,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${formatDate(cell.getValue())}</span>`;
          },
        },
      ],
    });
  } catch (err) {
    console.error(err);
  }
};

const emit = defineEmits(["update:value"]);

const updateValue = () => {
  emit('update:value', searchValue.value.value);
}

const searcher = () => {
  // console.log("searchConsole", this.searchValue.value)
  searchStatus.value = true;
  let dataList = tableTabulator.value;
  let keywords = searchValue.value.value;
  keywords.replace(/\s*/g, "");
  // console.log("SOURCE", dataList);
  // console.log("KEYWORDS", keywords);
  if (keywords == "") {
    console.log("NO DATA");
    dataList.clearFilter();
    searchValue.value.result = "";
  } else {
    console.log("SET FILTER", keywords);
    dataList.setFilter(matchAny, { value: keywords.replace(/\s*/g, "") });
    searchValue.value.result = keywords.replace(/\s*/g, "");
    searchValue.value.postStatus = searchValue.value.result;
    searchValue.value.value = "";
    updateValue(searchValue.value.value);
  }
};
const matchAny = (data, filterParams) => {
  // console.log("MATCH DATA", data);
  // console.log("MATCH filterParams", filterParams);
  var match = false;
  const regex = RegExp(filterParams.value, "i");

  for (var key in data) {
    if (regex.test(data[key]) == true) {
      match = true;
    }
  }
  return match;
};

const getIpcAllData = (data) => {
  try {
    console.log("IP table Data:", data);
    if (searchStatus.value == false) {
      searchValue.value.result = "";
      tableData.value = data.ipcs;
      updateTableData(data.ipcs);
      console.log("Table Data:", tableData.value);
      if (searchValue.value.postStatus != "") {
        searchValue.value.value = searchValue.value.postStatus;
        searcher();
      }
    }
  } catch (error) {
    console.log(error);
  }
};

const updateTableData = (newData) => {
  tableTabulator.value.replaceData(newData);
  tableTabulator.value.selectRow(selectedData.value);
};

watch(
  () => props.ipcAllData,
  (newVal) => {
    getIpcAllData(newVal);
  }  
);

watch(
  () => props.keyword,
  (newVal) => {
    searchValue.value.value = newVal;
  }
);

onMounted(() => {
  tableTabulatorFunc();
});

onBeforeUpdate(() => {
  if (tableTabulator.value) {
    tableTabulator.value.redraw();
  }
});

onBeforeUnmount(() => {
  tableTabulator.value.destroy();
});

defineExpose({ searcher });
</script>

<template>
  <div id="tableTabulator" ref="tableRef" class="mb-2"></div>
  <EditModal ref="editModalFunc" :dataIcon="modalIcon" :dataShow="editModalShow" />
  <DeleteModal ref="deleteModalFunc" :dataIcon="modalIcon" :dataShow="deleteModalShow" />
</template>
