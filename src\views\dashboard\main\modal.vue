<script setup>
import { ref } from "vue";
import {
    useToast
} from "vue-toastification";
import { ipcResetPort } from "@/apis/e84_Api";

const props = defineProps(["dataIcon", "dataShow", "dataMultiShow"]);
const toast = useToast();

const modalIcon = ref(props.dataIcon);
const devicePortId = ref(null);
const modalShow = ref(props.dataShow);
const multiModalShow = ref(props.dataMultiShow);

const showModal = (data) => {
    // console.log("Device ID:", data);
    devicePortId.value = data;
    modalShow.value = true;
};

const showMultiModal = (data) => {
    // console.log("Device ID:", data);
    devicePortId.value = data;
    multiModalShow.value = true;
};

const resetFunc = async () => {
    console.log("Reset Device Port:", devicePortId.value);
    try {
        const data = {
          port_id: devicePortId.value,
            enable: true,
        };
        console.log("RESET DATA:", data)

        const res = await ipcResetPort(data);
        console.log("Reset Device Port:", res);

        if(res.status == 200) {
            toast.success(`Reset Device Port ${devicePortId.value} Success` , {
                position: "bottom-right",
                timeout: 1000,
            });
        }

    } catch (error) {
        console.log("ERROR", error);
        toast.error(`ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]` , {
            position: "bottom-right",
            timeout: 1000,
        });
    }

    modalShow.value = false;
};

defineExpose({ showModal, showMultiModal });

</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body text-center pt-0">
      <i :class="[modalIcon]" class="fs-c-10 text-danger-emphasis"></i>
      <div class="mt-0 pt-0">
        <h4>Are you sure to Reset Device Port <span class="text-danger-emphasis">{{ devicePortId }}</span>？</h4>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
      <BButton type="button" variant="danger" class="me-5" @click="resetFunc">Yes</BButton>
      <BButton type="button" variant="light" @click="modalShow = false"
        >Close
      </BButton>
    </div>
    </div>
  </BModal>
  <BModal
    v-model="multiModalShow"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body text-center pt-0">
      <i :class="[modalIcon]" class="fs-c-10 text-danger-emphasis"></i>
      <div class="mt-0 pt-0">
        <h4>Are you sure to Reset Device Port <span class="text-danger-emphasis">{{ devicePortId }}</span>？</h4>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
      <BButton type="button" variant="danger" class="me-5" @click="resetFunc">Yes</BButton>
      <BButton type="button" variant="light" @click="modalShow = false"
        >Close
      </BButton>
    </div>
    </div>
  </BModal>
</template>