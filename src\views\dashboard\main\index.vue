<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch } from "vue";
import Layout from "@/layouts/main.vue";
import Widget from "./widget.vue";
import Tabulator from "./tabulator.vue";
import DataModal from "@/components/DataModal.vue";
import ConfirmModal from "@/components/ConfirmModal.vue";
import { useToast } from "vue-toastification";

import { 
  ipcStatus, 
  ipcDeviceStatus,
  resetBatchByPortId,
  automodeBatchByPortId,
  automodeBatchByDeviceId,
  resetBatchByDeviceId,
  manualmodeBatchByPortId,
  manualmodeBatchByDeviceId
} from "@/apis/e84_Api";

const toast = useToast();
// const { resetModalState } = useModalData(); // 已移除未使用的 resetModalState

const intervalStatus = ref(null);
let getStatusData = ref(null);
let getTableData = ref(null);

const tableModeCheck = ref(true);
const tableMode = computed(() => tableModeCheck.value ? 'Loadport' : 'IPC');



const tabulatorFunc = ref(null);
const searchQuery = ref("");
const searchKeywords = ref("");

// Modal states for unified DataModal
const resetModalShow = ref(false);
const autoModalShow = ref(false);
const manualModalShow = ref(false);

// Confirm Modal states
const confirmModalShow = ref(false);
const confirmModalData = ref({
  operation: 'Reset',
  tableMode: 'Loadport',
  selectedItems: [],
  ids: [],
  callback: null
});

// Modal functions
const showResetModal = () => {
  console.log('Reset button clicked, table mode:', tableMode.value);
  console.log('Raw data available for modal:', rawDataForModal.value?.length || 0, 'items');

  // 確保有資料才顯示Modal
  if (!rawDataForModal.value || rawDataForModal.value.length === 0) {
    console.warn('No data available for Reset modal');
    toast.warning('No data available, please try again later', {
      position: "bottom-right",
      timeout: 3000,
    });
    return;
  }

  resetModalShow.value = true;
};

const showAutoModal = () => {
  console.log('Auto button clicked, table mode:', tableMode.value);
  console.log('Raw data available for modal:', rawDataForModal.value?.length || 0, 'items');

  // 確保有資料才顯示Modal
  if (!rawDataForModal.value || rawDataForModal.value.length === 0) {
    console.warn('No data available for Auto modal');
    toast.warning('No data available, please try again later', {
      position: "bottom-right",
      timeout: 3000,
    });
    return;
  }

  autoModalShow.value = true;
};

const showManualModal = () => {
  console.log('Manual button clicked, table mode:', tableMode.value);
  console.log('Raw data available for modal:', rawDataForModal.value?.length || 0, 'items');

  // 確保有資料才顯示Modal
  if (!rawDataForModal.value || rawDataForModal.value.length === 0) {
    console.warn('No data available for Manual modal');
    toast.warning('No data available, please try again later', {
      position: "bottom-right",
      timeout: 3000,
    });
    return;
  }

  manualModalShow.value = true;
};

// Handle modal confirm events
const handleModalConfirm = async (eventData) => {
  console.log('Modal confirm event received:', eventData);
  console.log('handleModalConfirm - eventData.selectedItems:', eventData.selectedItems);
  console.log('handleModalConfirm - eventData.selectedItems.length:', eventData.selectedItems.length);
  console.log('handleModalConfirm - eventData.type:', eventData.type);
  
  try {
    // 根據 modal 類型和選擇的項目執行相應操作
    switch (eventData.type) {
      case 'reset':
        await handleResetOperation(eventData);
        break;
      case 'auto':
        await handleAutoOperation(eventData);
        break;
      case 'manual':
        await handleManualOperation(eventData);
        break;
      default:
        console.warn('Unknown modal type:', eventData.type);
    }
    
    // 刷新資料
    refreshData();
    
  } catch (error) {
    console.error('Modal operation failed:', error);
    toast.error(`Operation failed: ${error.message}`);
  }
};

// 提取並格式化 ID 列表
const extractAndFormatIds = (selectedItems, tableMode) => {
  const idField = tableMode === 'Loadport' ? 'port_id' : 'device_id';
  
  console.log('extractAndFormatIds - tableMode:', tableMode);
  console.log('extractAndFormatIds - idField:', idField);
  console.log('extractAndFormatIds - selectedItems:', selectedItems);
  
  if (selectedItems.length === 0) {
    console.error('extractAndFormatIds - No items selected');
    return null;
  }
  
  // 檢查每個項目的數據結構
  selectedItems.forEach((item, index) => {
    console.log(`Item ${index}:`, item);
    console.log(`Item ${index} has ${idField}:`, item[idField]);
  });
  
  const ids = selectedItems
    .map(item => item[idField])
    .filter(id => id !== undefined && id !== null && id !== '');
    
  console.log('extractAndFormatIds - extracted ids:', ids);
  
  if (ids.length === 0) {
    console.error('extractAndFormatIds - No valid IDs found in selected items');
    console.error('Available fields in first item:', Object.keys(selectedItems[0] || {}));
    return null;
  }
  
  const uniqueIds = [...new Set(ids)];
  console.log('extractAndFormatIds - unique ids:', uniqueIds);
  return uniqueIds.join(',');
};

// 顯示自定義確認對話框
const showConfirmDialog = (operation, selectedItems, tableMode, callback) => {
  const idField = tableMode === 'Loadport' ? 'port_id' : 'device_id';
  const ids = selectedItems
    .map(item => item[idField])
    .filter(id => id !== undefined && id !== null && id !== '')
    .filter((id, index, array) => array.indexOf(id) === index); // 去重

  confirmModalData.value = {
    operation,
    tableMode,
    selectedItems,
    ids,
    callback
  };
  
  confirmModalShow.value = true;
};

// 處理確認 Modal 的確認事件
const handleConfirmModalConfirm = () => {
  if (confirmModalData.value.callback) {
    confirmModalData.value.callback();
  }
  confirmModalShow.value = false;
};

// 處理確認 Modal 的取消事件
const handleConfirmModalCancel = () => {
  confirmModalShow.value = false;
  // 清空數據
  confirmModalData.value = {
    operation: '',
    tableMode: '',
    selectedItems: [],
    ids: [],
    callback: null
  };
};

// Reset 操作處理
const handleResetOperation = async (eventData) => {
  console.log('handleResetOperation - received eventData:', eventData);
  
  const { selectedItems, tableMode } = eventData;
  
  // 更詳細的檢查
  if (!selectedItems || selectedItems.length === 0) {
    console.error('handleResetOperation - No items selected');
    toast.warning('Please select items for Reset operation', {
      position: "bottom-right",
      timeout: 3000,
    });
    return;
  }
  
  console.log('handleResetOperation - proceeding with', selectedItems.length, 'items');
  console.log('handleResetOperation - first item structure:', selectedItems[0]);
  
  // 先關閉 DataModal，然後顯示 ConfirmModal
  resetModalShow.value = false;
  // 顯示自定義確認對話框
  showConfirmDialog('Reset', selectedItems, tableMode, () => executeResetOperation(selectedItems, tableMode));
};

// 執行 Reset 操作的具體邏輯
const executeResetOperation = async (selectedItems, tableMode) => {
  try {
    const idsString = extractAndFormatIds(selectedItems, tableMode);
    
    if (!idsString || idsString.trim() === '') {
      console.error('handleResetOperation - Invalid IDs extracted');
      console.error('Selected items structure:', selectedItems);
      toast.error('No valid IDs found in selected items, please check data integrity', {
        position: "bottom-right",
        timeout: 3000,
      });
      return;
    }
    
    // 根據 API 規格格式化請求數據
    const requestData = tableMode === 'Loadport' 
      ? { PortIDList: idsString, Enable: true }  // Reset = Manual mode
      : { DeviceIDList: idsString, Enable: true };
    
    console.log('handleResetOperation - requestData:', requestData);
    
    // 根據 tableMode 選擇對應的 API
    if (tableMode === 'Loadport') {
      await resetBatchByPortId(requestData);
    } else {
      await resetBatchByDeviceId(requestData);
    }
    
    toast.success(`Reset operation completed for ${selectedItems.length} items`, {
      position: "bottom-right",
      timeout: 3000,
    });
  } catch (error) {
    console.error('Reset operation failed:', error);
    toast.error(`Reset operation failed: ${error.message}`, {
      position: "bottom-right",
      timeout: 3000,
    });
    throw error;
  }
};

// Auto 操作處理
const handleAutoOperation = async (eventData) => {
  console.log('handleAutoOperation - received eventData:', eventData);
  
  const { selectedItems, tableMode } = eventData;
  
  // 更詳細的檢查
  if (!selectedItems || selectedItems.length === 0) {
    console.error('handleAutoOperation - No items selected');
    toast.warning('Please select items for Auto mode operation', {
      position: "bottom-right",
      timeout: 3000,
    });
    return;
  }
  
  console.log('handleAutoOperation - proceeding with', selectedItems.length, 'items');
  console.log('handleAutoOperation - first item structure:', selectedItems[0]);
  
  // 先關閉 DataModal，然後顯示 ConfirmModal
  autoModalShow.value = false;
  // 顯示自定義確認對話框
  showConfirmDialog('Auto', selectedItems, tableMode, () => executeAutoOperation(selectedItems, tableMode));
};

// 執行 Auto 操作的具體邏輯
const executeAutoOperation = async (selectedItems, tableMode) => {
  try {
    const idsString = extractAndFormatIds(selectedItems, tableMode);
    
    if (!idsString || idsString.trim() === '') {
      console.error('handleAutoOperation - Invalid IDs extracted');
      console.error('Selected items structure:', selectedItems);
      toast.error('No valid IDs found in selected items, please check data integrity', {
        position: "bottom-right",
        timeout: 3000,
      });
      return;
    }
    
    // 根據 API 規格格式化請求數據
    const requestData = tableMode === 'Loadport' 
      ? { PortIDList: idsString, Enable: true }   // Auto mode
      : { DeviceIDList: idsString, Enable: true };
    
    console.log('handleAutoOperation - requestData:', requestData);
    
    // 根據 tableMode 選擇對應的 API
    if (tableMode === 'Loadport') {
      await automodeBatchByPortId(requestData);
    } else {
      await automodeBatchByDeviceId(requestData);
    }
    
    toast.success(`Auto mode enabled for ${selectedItems.length} items`, {
      position: "bottom-right",
      timeout: 3000,
    });
  } catch (error) {
    console.error('Auto operation failed:', error);
    toast.error(`Auto mode operation failed: ${error.message}`, {
      position: "bottom-right",
      timeout: 3000,
    });
    throw error;
  }
};

// Manual 操作處理
const handleManualOperation = async (eventData) => {
  console.log('handleManualOperation - received eventData:', eventData);
  
  const { selectedItems, tableMode } = eventData;
  
  // 更詳細的檢查
  if (!selectedItems || selectedItems.length === 0) {
    console.error('handleManualOperation - No items selected');
    toast.warning('Please select items for Manual mode operation', {
      position: "bottom-right",
      timeout: 3000,
    });
    return;
  }
  
  console.log('handleManualOperation - proceeding with', selectedItems.length, 'items');
  console.log('handleManualOperation - first item structure:', selectedItems[0]);
  
  // 先關閉 DataModal，然後顯示 ConfirmModal
  manualModalShow.value = false;
  // 顯示自定義確認對話框
  showConfirmDialog('Manual', selectedItems, tableMode, () => executeManualOperation(selectedItems, tableMode));
};

// 執行 Manual 操作的具體邏輯
const executeManualOperation = async (selectedItems, tableMode) => {
  try {
    const idsString = extractAndFormatIds(selectedItems, tableMode);
    
    if (!idsString || idsString.trim() === '') {
      console.error('handleManualOperation - Invalid IDs extracted');
      console.error('Selected items structure:', selectedItems);
      toast.error('No valid IDs found in selected items, please check data integrity', {
        position: "bottom-right",
        timeout: 3000,
      });
      return;
    }
    
    // 根據 API 規格格式化請求數據
    const requestData = tableMode === 'Loadport' 
      ? { PortIDList: idsString, Enable: false }   // Manual mode
      : { DeviceIDList: idsString, Enable: false };
    
    console.log('handleManualOperation - requestData:', requestData);
    
    // 根據 tableMode 選擇對應的 API
    if (tableMode === 'Loadport') {
      await manualmodeBatchByPortId(requestData);
    } else {
      await manualmodeBatchByDeviceId(requestData);
    }
    
    toast.success(`Manual mode enabled for ${selectedItems.length} items`, {
      position: "bottom-right",
      timeout: 3000,
    });
  } catch (error) {
    console.error('Manual operation failed:', error);
    toast.error(`Manual mode operation failed: ${error.message}`, {
      position: "bottom-right",
      timeout: 3000,
    });
    throw error;
  }
};

// Handle modal cancel events
const handleModalCancel = () => {
  console.log('Modal cancelled');
  // DataModal 組件內部已經有完善的 cleanup 函數來處理狀態清理
  // 父組件不需要額外重置，避免影響主要 table 的資料顯示
  // setTimeout(() => {
  //   resetModalState();
  // }, 100);
};


// 為不同組件準備不同格式的資料
let rawDataForModal = ref(null);  // Modal 使用的原始資料
let processedDataForTable = ref(null);  // Tabulator 使用的處理後資料

const getIpcStatus = async (mode) => {
  try {
    console.log("GET DATA START:", mode)
    let res
    
    if(mode === 'Loadport') {
      // Loadport 模式：呼叫 ipcStatus API，取得原始的巢狀資料
      res = await ipcStatus();
      console.log("IPC LOADPORT RES:", res);
      const rawData = res.data.ipc_status;
      
      // 為 Widget 準備展開的資料
      const flattenedData = rawData.flatMap((item) =>
        Object.values(item.loadports)
      );
      
      // 為 Tabulator 準備展開且合併的資料
      const tableData = rawData.flatMap(device => {
          return Object.values(device.loadports).map(loadport => {
              return {
                  device_id: device.device_id,
                  name: device.name,
                  group: device.group,
                  ip: device.ip,
                  port: device.port,
                  ipc_enable: device.ipc_enable,
                  ftp_enable: device.ftp_enable,
                  ...loadport
              };
          });
      });
      
      getStatusData.value = flattenedData;  // Widget 使用
      processedDataForTable.value = tableData;  // Tabulator 使用
      rawDataForModal.value = rawData;  // Modal 使用原始資料
      getTableData.value = tableData;  // 暫時保持向後相容
    } else {
      // IPC 模式：呼叫 ipcDeviceStatus API
      res = await ipcDeviceStatus();
      console.log("IPC DEVICE RES:", res);
      const rawData = res.data.ipc_status;
      
      getStatusData.value = rawData;  // Widget 使用
      processedDataForTable.value = rawData;  // Tabulator 使用
      rawDataForModal.value = rawData;  // Modal 使用
      getTableData.value = rawData;  // 保持向後相容
    }

    console.log("Raw Data for Modal:", rawDataForModal.value);
    console.log("Processed Data for Table:", processedDataForTable.value);
    console.log("Data for Widget:", getStatusData.value);
  } catch (error) {
    console.log(error);
  }
};

const resetSearch = () => {
  searchQuery.value = "";
  searchKeywords.value = "";
  tabulatorFunc.value.searcher(searchQuery);
  // refreshData();
};

// provide('refreshData', refreshData)

const onKeyUpSearch = event => {
  if (event.key === 'Enter' && Object.values(searchQuery).filter(value => value !== '').length > 0) {
    searchKeywords.value = searchQuery.value;
    tabulatorFunc.value.searcher(searchQuery);    
  }
};

const updateValue = (value) => {
  searchQuery.value = value;
}

const startUpdateInterval = () => {
  stopUpdateInterval();
  intervalStatus.value = setInterval(() => {
    getIpcStatus(tableMode.value);
  }, 1000);
}

const stopUpdateInterval = () => {
  if (intervalStatus.value) {
    clearInterval(intervalStatus.value);
    intervalStatus.value = null;
  }
}

const refreshData = () => {
  console.log("REFRESH")
  getIpcStatus(tableMode.value);
}

watch(tableMode, (newMode) => {
  console.log(`Switched to mode: ${newMode}`);
  stopUpdateInterval();
  startUpdateInterval();
});

onMounted(() => {
  getIpcStatus(tableMode.value);
  startUpdateInterval()
});

onBeforeUnmount(() => {
  clearInterval(intervalStatus.value);
});

</script>
<template>
  <Layout>
    <BRow>
      <BCol xl="12">
        <Widget :ipcStatusData="getStatusData" />
      </BCol>
    </BRow>
    <BRow>
      <BCol xl="12">
        <BCard no-body>
          <BCardHeader class="py-2">
        <BRow class="align-items-center">
          <BCol sm="auto">
            <div class="d-flex align-items-center">
              <!-- <BCardTitle class="mb-0 flex-grow-1">{{ title }}</BCardTitle> -->
              <div class="me-3 fs-4 dark:text-white fw-bold">
                Main Dashboard
              </div>
              
              <!-- Function Buttons moved here -->
              <div class="d-flex gap-2 me-3">
                <!-- Reset 按鈕：只在 Loadport 模式顯示 -->
                <BButton
                  v-show="tableMode === 'Loadport'"
                  class="ms-2"
                  type="button"
                  variant="danger"
                  @click="showResetModal"
                >
                  <i class="ri-restart-line align-middle me-1"></i>
                  Reset
                </BButton>
                <!-- Auto 按鈕：兩種模式都顯示 -->
                <BButton
                  type="button"
                  class="ms-2"
                  variant="success"
                  @click="showAutoModal"
                >
                  <i class="ri-play-circle-line align-middle me-1"></i>
                  Auto
                </BButton>
                <!-- Manual 按鈕：兩種模式都顯示 -->
                <BButton
                  type="button"
                  class="ms-2"
                  variant="primary"
                  @click="showManualModal"
                >
                  <i class="ri-hand-coin-line align-middle me-1"></i>
                  Manual
                </BButton>
              </div>
              
              <div v-if="searchKeywords">
                <span class="badge bg-info-subtle text-info badge-border fs-6">Search Keyword: {{ searchKeywords }}</span>
              </div>
            </div>
          </BCol>
          <BCol sm>
            <div class="d-flex justify-content-sm-end align-items-center">
              
              <b-form-checkbox v-model="tableModeCheck" name="check-button" switch size="lg" class="d-flex text-primary align-items-center">
                Mode : {{ tableMode == "Loadport" ? "Loadport" : "IPC" }}
              </b-form-checkbox>
              <!-- <BButton
                type="button"
                class="me-2"
                variant="success"
                v-throttle="refreshData"
                :delay="300"
              >
                <i class="ri-refresh-line align-middle me-1"></i>
                Refresh
              </BButton> -->
              <div class="search-box ms-2">
                <input
                  type="text"
                  class="form-control"
                  id="searchResultList"
                  placeholder="Search ..."
                  v-model="searchQuery"
                  @keyup="onKeyUpSearch"
                  on
                />
                <i class="ri-search-line search-icon"></i>
              </div>
              <BButton
                type="button"
                class="ms-2"
                variant="success"
                v-throttle="resetSearch"
                :delay="300"
              >
                Search Reset
              </BButton>
            </div>
          </BCol>
        </BRow>
      </BCardHeader>
          <BCardBody>
            <div class="table-responsive table-card">
              <Tabulator ref="tabulatorFunc" :ipcStatusData="getTableData" :mode="tableMode" :keyword="searchQuery" @update:value="updateValue" @startInterval="startUpdateInterval" @stopInterval="stopUpdateInterval" @refresh="refreshData" />
            </div>
          </BCardBody>
        </BCard>
      </BCol>
    </BRow>
    
    <!-- Unified DataModal Components -->
    <DataModal
      v-model:show="resetModalShow"
      type="reset"
      :data="rawDataForModal || []"
      :table-mode="tableMode"
      @confirm="handleModalConfirm"
      @cancel="handleModalCancel"
    />
    
    <DataModal
      v-model:show="autoModalShow"
      type="auto"
      :data="rawDataForModal || []"
      :table-mode="tableMode"
      @confirm="handleModalConfirm"
      @cancel="handleModalCancel"
    />
    
    <DataModal
      v-model:show="manualModalShow"
      type="manual"
      :data="rawDataForModal || []"
      :table-mode="tableMode"
      @confirm="handleModalConfirm"
      @cancel="handleModalCancel"
    />

    <!-- Custom Confirm Modal -->
    <ConfirmModal
      v-model:show="confirmModalShow"
      :operation="confirmModalData.operation"
      :table-mode="confirmModalData.tableMode"
      :selected-items="confirmModalData.selectedItems"
      :ids="confirmModalData.ids"
      @confirm="handleConfirmModalConfirm"
      @cancel="handleConfirmModalCancel"
    />
  </Layout>
</template>
