<script setup>
import { reactive, ref, watch, inject } from "vue";
import { useToast } from "vue-toastification";
import { addAccountData } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();


const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const addData = reactive({
  userid: "",
  name: "",
  acc_type: "",
  active: true,
  password: "",
  passwordConfirm: ""
});

const showModal = () => {
  modalShow.value = true;
};


const resetForm = () => {
      addData.userid = ""
      addData.name = ""
      addData.acc_type = ""
      addData.password = ""
      addData.passwordConfirm = ""
      addData.active = false,
      showMismatchMessage.value = false
      passwordTooShort.value = false
    }

const addUserFunc = async () => {
  console.log("Add User:", addData);
  try {
    const data = {
      userid: addData.userid,
      name: addData.name,
      acc_type: addData.acc_type,
      password: addData.password,
      passwordConfirm: addData.passwordConfirm,
      active: addData.active,
    };
    console.log("ADD User:", data);

    const res = await addAccountData(data);
    console.log("Add User Res:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Add User ${addData.code} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.status}: [${error.data.detail}. ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

const showMismatchMessage = ref(false)
const passwordTooShort = ref(false)
let mismatchTimeout = null

const checkPasswordMismatch = () => {
  // 檢查密碼長度
  if (addData.password) {
    passwordTooShort.value = addData.password.length < 8
  } else {
    passwordTooShort.value = false
  }

  // 檢查密碼和確認密碼是否匹配
  if (addData.password && addData.passwordConfirm) {
    showMismatchMessage.value = addData.password !== addData.passwordConfirm
  } else {
    showMismatchMessage.value = false
  }
}

watch(addData, () => {
  console.log("CHECK")
  if (mismatchTimeout) clearTimeout(mismatchTimeout)
  mismatchTimeout = setTimeout(checkPasswordMismatch, 1000)
})

watch(
  () => modalShow.value,
  (newValue) => {
    if (newValue === false) {
      resetForm();
    }
  }
);



defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Add User"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3">
        <BCol xxl="6">
          <div>
            <label for="acc_type" class="form-label">User Type</label>
            <BFormSelect v-model="addData.acc_type" class="form-select mb-3"
                    aria-label="Default select">
                    <BFormSelectOption :value="null">Select User Type</BFormSelectOption>
                    <BFormSelectOption value="ADMIN">ADMIN</BFormSelectOption>
                    <BFormSelectOption value="ME">ME</BFormSelectOption>
                    <BFormSelectOption value="OP">OP</BFormSelectOption>
                    <BFormSelectOption value="PE">PE</BFormSelectOption>
                  </BFormSelect>
          </div>
        </BCol>
      </BRow>
      <BRow class="g-3 mb-3">
        <BCol xxl="6">
          <div>
            <label for="userid" class="form-label">User ID</label>
            <input
              v-model="addData.userid"
              type="string"
              class="form-control"
              id="userid"
              placeholder="Enter your user ID"
            />
          </div>
        </BCol>
        <BCol xxl="6">
          <label for="name" class="form-label">Name</label>
          <input
            v-model="addData.name"
            type="string"
            class="form-control"
            id="name"
            placeholder="Enter your name"
          />
        </BCol>
        <BCol xxl="6">
          <label for="password" class="form-label">Password</label>
          <input
            v-model="addData.password"
            type="text"
            class="form-control"
            id="password"
            placeholder="Enter your password"
          />
          <div v-if="passwordTooShort" class="text-danger">
            Password must be at least 8 characters
          </div>
        </BCol>
        <BCol xxl="6">
          <label for="passwordConfirm" class="form-label">Confirm Password</label>
          <input
            v-model="addData.passwordConfirm"
            type="text"
            class="form-control"
            id="passwordConfirm"
            placeholder="Confirm your password"
          />
          <div v-if="showMismatchMessage" class="text-danger">
            Passwords do not match
          </div>
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          
        </BCol>
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="active"
              v-model="addData.active"
              switch
              class="form-switch-md me-2"
            >
              Active
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="addUserFunc" :disabled="!addData.userid || !addData.name || !addData.acc_type || !addData.password || !addData.passwordConfirm"
          >Submit</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
