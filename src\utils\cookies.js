import { useCookies } from '@vueuse/integrations/useCookies'

const cookieToken = useCookies(['loginToken'])

/**
 * 清除 cookie 的 token
 */
export const clearCookieToken = () => {
  cookieToken.remove('loginToken')
}

/**
 * 取得 cookie 的 token
 * @returns {string}
 */
export const getCookieToken = () => {
  return cookieToken.get('loginToken')
}

/**
 * 設置 cookie 的 token
 * @param {string} token token
 */
export const setCookieToken = (token) => {
  const date = new Date()
  date.setTime(date.getTime() + 1000 * 60 * 30)
  cookieToken.set('loginToken', token, { expires: date })
}