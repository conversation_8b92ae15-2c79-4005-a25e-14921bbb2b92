<script setup>
import { ref, watch } from "vue";
import {
    useToast
} from "vue-toastification";
import { setManualMode } from "@/apis/e84_Api"; // 待後端 API 完成後取消註解

const props = defineProps(["dataShow"]);
const emit = defineEmits(["update:dataShow"]);
const toast = useToast();

const modalShow = ref(false);

// Watch for props changes
watch(() => props.dataShow, (newVal) => {
  console.log('manualModal props.dataShow changed:', newVal);
  modalShow.value = newVal;
  console.log('manualModal modalShow updated to:', modalShow.value);
}, { immediate: true });
const isLoading = ref(false);

const manualFunc = async () => {
    console.log("Manual Mode Function Called");
    isLoading.value = true;
    
    try {
        // 待後端 API 完成後實作
        const res = await setManualMode();
        console.log("Set Manual Mode:", res);
        
        // 模擬 API 呼叫
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        toast.success("Manual Mode Enabled Successfully", {
            position: "bottom-right",
            timeout: 2000,
        });
        
        modalShow.value = false;
        emit('update:dataShow', false);
        
    } catch (error) {
        console.log("ERROR", error);
        toast.error(`ERROR: Manual Mode Setup Failed`, {
            position: "bottom-right",
            timeout: 2000,
        });
    } finally {
        isLoading.value = false;
    }
};

const closeModal = () => {
    console.log('manualModal closeModal called, emitting update:dataShow false');
    modalShow.value = false;
    emit('update:dataShow', false);
};

defineExpose({ modalShow });

</script>
<template>
  <BModal
    :model-value="modalShow"
    @update:model-value="(value) => { if (!value) closeModal(); }"
    hide-footer
    class="v-modal-custom"
    centered
    no-close-on-backdrop
    title="Enable Manual Mode"
  >
    <div class="modal-body text-center pt-0">
      <i class="ri-hand-coin-line fs-c-10 text-primary-emphasis"></i>
      <div class="mt-3 pt-0">
        <h4>Enable Manual Mode for All Devices?</h4>
        <p class="text-muted mt-2">This will allow manual control of all device operations. Auto mode will be disabled.</p>
      </div>
      <div class="modal-footer v-modal-footer justify-content-center">
        <BButton 
          type="button" 
          variant="primary" 
          class="me-3" 
          @click="manualFunc"
          :disabled="isLoading"
        >
          <span v-if="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ isLoading ? 'Enabling...' : 'Yes, Enable Manual' }}
        </BButton>
        <BButton 
          type="button" 
          variant="light" 
          @click="closeModal"
          :disabled="isLoading"
        >
          Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>