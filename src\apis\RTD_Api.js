import axios from "axios";
import {
    useToast
} from "vue-toastification";

const toast = useToast();

const localConfig = localStorage.getItem("serverConfig") || [];

const instance = axios.create({
    baseURL: `http://${localConfig.RTD.ip_address}`,
    timeout: 5000,
    headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
    }
});

if (localConfig.user_token) {
    instance.defaults.headers.common['Authorization'] = `Bearer ${localConfig.user_token}`;
}

export const RTD_Request = async (url, method = 'get', data = null) => {
    const axiosInstance = instance;
    axiosInstance.interceptors.response.use(response => {
        if (response.status < 400) {
            // console.log("Instance RESPONSE < 400：", response)
            return Promise.resolve(response);
        }
        else {
            // console.log("Instance RESPONSE > 400：", response)
            return Promise.reject(response)
        }
    }, error => {
        if (error.response) {
            // console.log("ERROR", error)
            // console.log("GET API ERROR", error.response)
            toast.error(`ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]` , {
            position: "bottom-right",
            timeout: 1000,
            });
            return Promise.reject(error.response);
        }
    })
    try {
        const response = await axiosInstance.request({
            url,
            method,
            data
        });
        return response;
    } catch (error) {
        console.error("Api Js Catch Error", error);
        return Promise.reject(error)
    }
}

// Data
export const authLogin = async (data) => RTD_Request(`/api/user/login`, 'post', data);

