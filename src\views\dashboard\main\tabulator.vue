<script setup>
import { onMounted, onBeforeUnmount, onBeforeUpdate, ref, watch, reactive } from "vue";
import { TabulatorFull as Tabulator } from "tabulator-tables";
import ModalConfirm from "./modal.vue";
import { tabulatorModeFormatter } from "@/utils/modeFormatter";

const props = defineProps(["ipcStatusData", "mode", "keyword"]);

let tableData = ref([]);
let tableRef = ref(null);
let tableTabulator = ref(null);
let selectedData = [];
let modalShow = ref(false);
const modalIcon = ref("ri-alert-line");
const modalFunc = ref(null);

let searchStatus = ref(false);
let searchValue = ref({
  value: "",
  result: "",
  postStatus: "",
});

const columnDefinitions = reactive({
  Loadport: [
        {
          title: "Device ID",
          field: "device_id",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 150,
        },
        {
          title: "Name",
          field: "name",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 150,
        },
        {
          title: "Group",
          field: "group",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 150,
        },
        {
          title: "IP",
          field: "ip",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 150,
        },
        {
          title: "Port",
          field: "port",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 80,
        },
        {
          title: "Port ID",
          field: "port_id",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
        },
        {
          title: "Port No.",
          field: "port_no",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 100,
        },
        {
          title: "Port State",
          field: "port_state",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 120,
        },
        {
          title: "IPC Enable",
          field: "ipc_enable",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
          formatter: function (cell) {
            return cell.getValue() == true ? `<span class="badge text-bg-success rounded-pill text-uppercase">${ cell.getValue() }</span>` : `<span class="badge text-bg-danger rounded-pill text-uppercase">${ cell.getValue() }</span>`;
          },
        },
        {
          title: "Mode",
          field: "mode",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
          formatter: tabulatorModeFormatter,
        },
        // {
        //   title: "FTP Enable",
        //   field: "ftp_enable",
        //   headerHozAlign: "center",
        //   hozAlign: "center",
        //   vertAlign : "middle",
        //   width: 150,
        //   formatter: function (cell) {
        //     return cell.getValue() == true ? `<span class="badge text-bg-success rounded-pill text-uppercase">${ cell.getValue() }</span>` : `<span class="badge text-bg-danger rounded-pill text-uppercase">${ cell.getValue() }</span>`;
        //   },
        // },
        {
          title: "Status",
          field: "status",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
          formatter: function (cell) {
            return cell.getValue().toLowerCase() == "online" ? `<span class="badge text-bg-success rounded-pill text-uppercase">${ cell.getValue() }</span>` : cell.getValue().toLowerCase() == "offline" ? `<span class="badge text-bg-danger rounded-pill text-uppercase">${ cell.getValue() }</span>` : cell.getValue();
          },
        },
        {
          title: "Detail",
          field: "detail",
          headerHozAlign: "center",
          hozAlign: "center",
        },
      ],
  IPC: [
      {
        title: "Device ID",
        field: "device_id",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "Name",
        field: "name",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "Group",
        field: "group",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "IP",
        field: "ip",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "Port",
        field: "port",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "IPC Enable",
        field: "ipc_enable",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      // {
      //   title: "FTP Enable",
      //   field: "ftp_enable",
      //   headerHozAlign: "center",
      //   hozAlign: "center",
      //   width: 150,
      // },
      {
        title: "Status",
        field: "status",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "Detail",
        field: "detail",
        headerHozAlign: "center",
        hozAlign: "center",
      }
  ]
})




const tableTabulatorFunc = () => {
  try {
    if (tableTabulator.value) {
      tableTabulator.value.destroy();
    }

    // 根據模式設置不同的 index 字段
    const indexField = props.mode === 'Loadport' ? 'port_id' : 'device_id';

    tableTabulator.value = new Tabulator(tableRef.value, {
      placeholder: "No Data Available",
      data: tableData.value,
      pagination: "remote",
      paginationSize: 10,
      rowHeight: 42,
      paginationSizeSelector: [10, 25, 50, 100],
      paginationCounter: "rows",
      layout: "fitColumns", //fitData, fitDataFill, fitDataTable, fitDataStretch, fitColumns
      autoResize: false,
      reactiveData: true,
      dataLoader: true,
      height: 562,
      progressiveLoadDelay: 200,
      index: indexField, // 添加 index 配置，讓 updateOrAddData 能正確識別資料行
      columns: columnDefinitions[props.mode],
      tableBuilt: function() {
        updateTableData();
      }
    });
  } catch (err) {
    console.error(err);
  }
};

const getIpcStatus = (data) => {
  try {
    console.log("IP table Data:", data);
    

    if (searchStatus.value == false) {
      searchValue.value.result = "";
      tableData.value = data;
      updateTableData(data);

      if (searchValue.value.postStatus != "") {
        searchValue.value.value = searchValue.value.postStatus;
        searcher();
      }
    }
    
    console.log("Table Data:", tableData.value);
  } catch (error) {
    console.log(error);
  }
};

const emit = defineEmits(["update:value", 'startInterval', 'stopInterval', 'refresh']);

const updateValue = () => {
  emit('update:value', searchValue.value.value);
}

const startInterval = () => {
  emit('startInterval')
}

const stopInterval = () => {
  emit('stopInterval')
}

const refresh = () => {
  emit('refresh')
}

const updateTableData = (newData) => {
  try {
    console.log('updateTableData called with:', newData?.length, 'items');
    
    if (!tableTabulator.value) {
      console.error('Tabulator instance not available');
      return;
    }

    const currentDataLength = tableTabulator.value.getData().length;
    console.log('Current table data length:', currentDataLength);

    if (currentDataLength === 0) {
      console.log('Using setData for initial load');
      tableTabulator.value.setData(newData);
    } else {
      console.log('Using updateOrAddData for incremental update');
      tableTabulator.value.updateOrAddData(newData)
        .then(function(rows) {
          console.log('Successfully updated/added rows:', rows.length);
        })
        .catch(function(error) {
          console.error('updateOrAddData failed:', error);
          // 失敗時回退到 setData，但會重置滾動位置
          console.log('Falling back to setData');
          tableTabulator.value.setData(newData);
        });
    }
    
    // 保持選中狀態
    if (selectedData.value && selectedData.value.length > 0) {
      tableTabulator.value.selectRow(selectedData.value);
    }
  } catch (error) {
    console.error('updateTableData error:', error);
  }
};


const searcher = () => {
  // console.log("searchConsole", this.searchValue.value)
  searchStatus.value = true;
  let dataList = tableTabulator.value;
  let keywords = searchValue.value.value;
  keywords.replace(/\s*/g, "");
  // console.log("SOURCE", dataList);
  // console.log("KEYWORDS", keywords);
  if (keywords == "") {
    console.log("NO DATA");
    dataList.clearFilter();
    searchValue.value.result = "";
  } else {
    console.log("SET FILTER", keywords);
    dataList.setFilter(matchAny, { value: keywords.replace(/\s*/g, "") });
    searchValue.value.result = keywords.replace(/\s*/g, "");
    searchValue.value.postStatus = searchValue.value.result;
    searchValue.value.value = "";
    updateValue(searchValue.value.value);
  }
};
const matchAny = (data, filterParams) => {
  // console.log("MATCH DATA", data);
  // console.log("MATCH filterParams", filterParams);
  var match = false;
  const regex = RegExp(filterParams.value, "i");

  for (var key in data) {
    if (regex.test(data[key]) == true) {
      match = true;
    }
  }
  return match;
};


watch(
  () => props.ipcStatusData,
  (newVal) => {
    getIpcStatus(newVal);
  }
);

watch(
  () => props.keyword,
  (newVal) => {
    searchValue.value.value = newVal;
  }
);

watch(
  () => props.mode,
  (newMode) => {
  stopInterval()
  // await tableTabulatorFunc();  // 重新初始化表格
  console.log('Switched to mode tab:', newMode);
  tableTabulator.value.setColumns(columnDefinitions[newMode])
  refresh(newMode)
  startInterval();
});

onBeforeUpdate(() => {
  if (tableTabulator.value) {
    tableTabulator.value.redraw();
  }
});

onMounted(() => {
  tableTabulatorFunc();
});

onBeforeUnmount(() => {
  tableTabulator.value.destroy();
});

defineExpose({ searcher });
</script>

<template>
  <div id="tableTabulator" ref="tableRef" class="mb-2"></div>
  <ModalConfirm ref="modalFunc" :dataIcon="modalIcon" :dataShow="modalShow" />
</template>