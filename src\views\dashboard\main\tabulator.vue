<script setup>
import { onMounted, onBeforeUnmount, onBeforeUpdate, ref, watch, reactive, nextTick } from "vue";
import { TabulatorFull as Tabulator } from "tabulator-tables";
import ModalConfirm from "./modal.vue";
import { tabulatorModeFormatter } from "@/utils/modeFormatter";

const props = defineProps(["ipcStatusData", "mode", "keyword"]);

let tableData = ref([]);
let tableRef = ref(null);
let tableTabulator = ref(null);
let selectedData = [];
let modalShow = ref(false);
const modalIcon = ref("ri-alert-line");
const modalFunc = ref(null);

let searchStatus = ref(false);
let searchValue = ref({
  value: "",
  result: "",
  postStatus: "",
});

const columnDefinitions = reactive({
  Loadport: [
        {
          title: "Device ID",
          field: "device_id",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 150,
        },
        {
          title: "Name",
          field: "name",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 150,
        },
        {
          title: "Group",
          field: "group",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 150,
        },
        {
          title: "IP",
          field: "ip",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 150,
        },
        {
          title: "Port",
          field: "port",
          headerHozAlign: "center",
          hozAlign: "center",
          width: 80,
        },
        {
          title: "Port ID",
          field: "port_id",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
        },
        {
          title: "Port No.",
          field: "port_no",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 100,
        },
        {
          title: "Port State",
          field: "port_state",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 120,
        },
        // {
        //   title: "IPC Enable",
        //   field: "ipc_enable",
        //   headerHozAlign: "center",
        //   hozAlign: "center",
        //   vertAlign : "middle",
        //   width: 150,
        //   formatter: function (cell) {
        //     return cell.getValue() == true ? `<span class="badge text-bg-success rounded-pill text-uppercase">${ cell.getValue() }</span>` : `<span class="badge text-bg-danger rounded-pill text-uppercase">${ cell.getValue() }</span>`;
        //   },
        // },
        {
          title: "Mode",
          field: "mode",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
          formatter: tabulatorModeFormatter,
        },
        // {
        //   title: "FTP Enable",
        //   field: "ftp_enable",
        //   headerHozAlign: "center",
        //   hozAlign: "center",
        //   vertAlign : "middle",
        //   width: 150,
        //   formatter: function (cell) {
        //     return cell.getValue() == true ? `<span class="badge text-bg-success rounded-pill text-uppercase">${ cell.getValue() }</span>` : `<span class="badge text-bg-danger rounded-pill text-uppercase">${ cell.getValue() }</span>`;
        //   },
        // },
        {
          title: "Status",
          field: "status",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign : "middle",
          width: 150,
          formatter: function (cell) {
            return cell.getValue().toLowerCase() == "online" ? `<span class="badge text-bg-success rounded-pill text-uppercase">${ cell.getValue() }</span>` : cell.getValue().toLowerCase() == "offline" ? `<span class="badge text-bg-danger rounded-pill text-uppercase">${ cell.getValue() }</span>` : cell.getValue();
          },
        },
        {
          title: "Detail",
          field: "detail",
          headerHozAlign: "center",
          hozAlign: "center",
        },
      ],
  IPC: [
      {
        title: "Device ID",
        field: "device_id",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "Name",
        field: "name",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "Group",
        field: "group",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "IP",
        field: "ip",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "Port",
        field: "port",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "IPC Enable",
        field: "ipc_enable",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      // {
      //   title: "FTP Enable",
      //   field: "ftp_enable",
      //   headerHozAlign: "center",
      //   hozAlign: "center",
      //   width: 150,
      // },
      {
        title: "Status",
        field: "status",
        headerHozAlign: "center",
        hozAlign: "center",
        width: 150,
      },
      {
        title: "Detail",
        field: "detail",
        headerHozAlign: "center",
        hozAlign: "center",
      }
  ]
})




const tableTabulatorFunc = () => {
  try {
    console.log('tableTabulatorFunc called for mode:', props.mode);

    // 檢查 DOM 元素是否存在
    if (!tableRef.value) {
      console.error('Table DOM element not available');
      return;
    }

    if (tableTabulator.value) {
      console.log('Destroying existing Tabulator instance');
      tableTabulator.value.destroy();
      tableTabulator.value = null;
    }

    // 根據模式設置不同的 index 字段
    const indexField = props.mode === 'Loadport' ? 'port_id' : 'device_id';
    console.log('Creating Tabulator with index field:', indexField);

    tableTabulator.value = new Tabulator(tableRef.value, {
      placeholder: "No Data Available",
      data: [],  // 初始化時不載入資料，避免衝突
      pagination: "remote",
      paginationSize: 10,
      rowHeight: 42,
      paginationSizeSelector: [10, 25, 50, 100],
      paginationCounter: "rows",
      layout: "fitColumns", //fitData, fitDataFill, fitDataTable, fitDataStretch, fitColumns
      autoResize: false,
      reactiveData: true,
      dataLoader: true,
      height: 562,
      progressiveLoadDelay: 200,
      index: indexField, // 添加 index 配置，讓 updateOrAddData 能正確識別資料行
      columns: columnDefinitions[props.mode],
      tableBuilt: function() {
        console.log('Tabulator built successfully for mode:', props.mode, 'with index:', indexField);
        // 表格建立後，重置分頁到第一頁
        if (tableTabulator.value) {
          tableTabulator.value.setPage(1);
        }
      }
    });

    console.log('Tabulator instance created successfully');
  } catch (err) {
    console.error('Error creating Tabulator:', err);
  }
};

const getIpcStatus = (data) => {
  try {
    console.log("IP table Data:", data);
    

    if (searchStatus.value == false) {
      searchValue.value.result = "";
      tableData.value = data;
      updateTableData(data);

      if (searchValue.value.postStatus != "") {
        searchValue.value.value = searchValue.value.postStatus;
        searcher();
      }
    }
    
    console.log("Table Data:", tableData.value);
  } catch (error) {
    console.log(error);
  }
};

const emit = defineEmits(["update:value", 'startInterval', 'stopInterval', 'refresh']);

const updateValue = () => {
  emit('update:value', searchValue.value.value);
}

const startInterval = () => {
  emit('startInterval')
}

const stopInterval = () => {
  emit('stopInterval')
}

const refresh = () => {
  emit('refresh')
}

// 追蹤當前模式和索引字段
let currentMode = ref(props.mode);
let currentIndexField = ref(props.mode === 'Loadport' ? 'port_id' : 'device_id');

const updateTableData = (newData) => {
  try {
    console.log('updateTableData called with:', newData?.length, 'items');
    console.log('Current mode:', props.mode, 'Previous mode:', currentMode.value);

    if (!tableTabulator.value) {
      console.error('Tabulator instance not available');
      return;
    }

    // 如果沒有資料，直接清空表格
    if (!newData || newData.length === 0) {
      console.log('No data provided, clearing table');
      tableTabulator.value.setData([]);
      return;
    }

    const currentDataLength = tableTabulator.value.getData().length;
    const newIndexField = props.mode === 'Loadport' ? 'port_id' : 'device_id';

    // 檢測模式或索引字段是否改變
    const modeChanged = currentMode.value !== props.mode;
    const indexFieldChanged = currentIndexField.value !== newIndexField;

    if (modeChanged || indexFieldChanged) {
      console.log('Mode/Index changed - Mode:', currentMode.value, '→', props.mode,
                  'Index:', currentIndexField.value, '→', newIndexField);
      currentMode.value = props.mode;
      currentIndexField.value = newIndexField;
    }

    // 檢查資料結構是否與當前索引字段匹配
    const dataStructureMatches = newData && newData.length > 0 &&
                                 Object.prototype.hasOwnProperty.call(newData[0], currentIndexField.value);

    console.log('Data structure matches current index field:', dataStructureMatches);
    console.log('Current index field:', currentIndexField.value);
    console.log('Sample data keys:', newData && newData.length > 0 ? Object.keys(newData[0]) : 'No data');

    // 使用 setData 的條件：
    // 1. 初始載入 (currentDataLength === 0)
    // 2. 模式改變 (modeChanged)
    // 3. 索引字段改變 (indexFieldChanged)
    // 4. 資料結構不匹配當前索引字段 (!dataStructureMatches)
    const shouldUseSetData = currentDataLength === 0 || modeChanged || indexFieldChanged || !dataStructureMatches;

    if (shouldUseSetData) {
      const reason = currentDataLength === 0 ? 'initial load' :
                    modeChanged ? 'mode change' :
                    indexFieldChanged ? 'index field change' :
                    'data structure mismatch';
      console.log('Using setData for:', reason);

      try {
        tableTabulator.value.setData(newData);
        console.log('setData completed successfully');
      } catch (setDataError) {
        console.error('setData failed:', setDataError);
      }
    } else {
      console.log('Using updateOrAddData for incremental update');
      tableTabulator.value.updateOrAddData(newData)
        .then(function(rows) {
          console.log('Successfully updated/added rows:', rows.length);
        })
        .catch(function(error) {
          console.error('updateOrAddData failed:', error);
          // 失敗時回退到 setData
          console.log('Falling back to setData due to updateOrAddData failure');
          try {
            tableTabulator.value.setData(newData);
            console.log('Fallback setData completed successfully');
          } catch (fallbackError) {
            console.error('Fallback setData also failed:', fallbackError);
          }
        });
    }

    // 保持選中狀態
    if (selectedData.value && selectedData.value.length > 0) {
      tableTabulator.value.selectRow(selectedData.value);
    }
  } catch (error) {
    console.error('updateTableData error:', error);
  }
};


const searcher = () => {
  // console.log("searchConsole", this.searchValue.value)
  searchStatus.value = true;
  let dataList = tableTabulator.value;
  let keywords = searchValue.value.value;
  keywords.replace(/\s*/g, "");
  // console.log("SOURCE", dataList);
  // console.log("KEYWORDS", keywords);
  if (keywords == "") {
    console.log("NO DATA");
    dataList.clearFilter();
    searchValue.value.result = "";
  } else {
    console.log("SET FILTER", keywords);
    dataList.setFilter(matchAny, { value: keywords.replace(/\s*/g, "") });
    searchValue.value.result = keywords.replace(/\s*/g, "");
    searchValue.value.postStatus = searchValue.value.result;
    searchValue.value.value = "";
    updateValue(searchValue.value.value);
  }
};
const matchAny = (data, filterParams) => {
  // console.log("MATCH DATA", data);
  // console.log("MATCH filterParams", filterParams);
  var match = false;
  const regex = RegExp(filterParams.value, "i");

  for (var key in data) {
    if (regex.test(data[key]) == true) {
      match = true;
    }
  }
  return match;
};


watch(
  () => props.ipcStatusData,
  (newVal) => {
    getIpcStatus(newVal);
  }
);

watch(
  () => props.keyword,
  (newVal) => {
    searchValue.value.value = newVal;
  }
);

watch(
  () => props.mode,
  async (newMode, oldMode) => {
    try {
      console.log('Mode change detected:', oldMode, '→', newMode);

      stopInterval()
      console.log('Stopped interval for mode change');

      // 更新追蹤的模式狀態
      currentMode.value = newMode;
      currentIndexField.value = newMode === 'Loadport' ? 'port_id' : 'device_id';
      console.log('Updated tracking state - mode:', currentMode.value, 'indexField:', currentIndexField.value);

      // 由於 index 字段改變，必須重新初始化 Tabulator
      // 但要確保 DOM 元素存在且穩定
      if (tableTabulator.value) {
        console.log('Destroying existing Tabulator for mode change');
        tableTabulator.value.destroy();
        tableTabulator.value = null;
      }

      // 等待一個 tick 確保 DOM 穩定
      await nextTick();

      // 檢查 DOM 元素是否存在
      if (tableRef.value) {
        console.log('Reinitializing Tabulator with new index field');
        tableTabulatorFunc();
        console.log('Tabulator reinitialized successfully');
      } else {
        console.error('Table DOM element not available for reinitialization');
      }

      refresh(newMode)
      console.log('Refresh called for new mode');

      startInterval();
      console.log('Started interval for new mode');
    } catch (error) {
      console.error('Error during mode change:', error);
      // 即使出錯也要嘗試重啟間隔
      try {
        startInterval();
      } catch (intervalError) {
        console.error('Failed to restart interval:', intervalError);
      }
    }
  }
);

onBeforeUpdate(() => {
  if (tableTabulator.value) {
    tableTabulator.value.redraw();
  }
});

onMounted(() => {
  tableTabulatorFunc();
});

onBeforeUnmount(() => {
  tableTabulator.value.destroy();
});

defineExpose({ searcher });
</script>

<template>
  <div id="tableTabulator" ref="tableRef" class="mb-2"></div>
  <ModalConfirm ref="modalFunc" :dataIcon="modalIcon" :dataShow="modalShow" />
</template>