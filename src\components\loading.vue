<script setup>
import { ref, watch } from "vue";
const props = defineProps(["isLoading"]);

let loadingStatus = ref(false);

watch(
  () => props.isLoading,
  (newVal) => {
    loadingStatus.value = newVal;
  }
);
</script>
<template>
  <Loading
    v-model:active="loadingStatus"
    :is-full-page="false"
    :background-color="'#000000'"
  >
    <span class="spinner-border flex-shrink-0" role="status">
      <span class="visually-hidden">Loading...</span>
    </span>
  </Loading>
</template>
