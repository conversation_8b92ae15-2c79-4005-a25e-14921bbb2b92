<script setup>
import { computed, onMounted } from "vue";
import { useLayoutStore } from "@/store/modules/helpers";
import { initTheme } from "@/utils/theme";

const useLayout = useLayoutStore();

const layoutType = computed(() => {
  return useLayout.layoutType;
});

initTheme();

const removeActivation = (items) => {
  items.forEach((item) => {
    if (item.classList.contains("menu-link")) {
      if (!item.classList.contains("active")) {
        item.setAttribute("aria-expanded", false);
      }
      item.nextElementSibling.classList.remove("show");
    }
    if (item.classList.contains("nav-link")) {
      if (item.nextElementSibling) {
        item.nextElementSibling.classList.remove("show");
      }
      item.setAttribute("aria-expanded", false);
    }
    item.classList.remove("active");
  });
};

const activateParentDropdown = (item) => {
  // navbar-nav menu add active
  item.classList.add("active");
  let parentCollapseDiv = item.closest(".collapse.menu-dropdown");
  if (parentCollapseDiv) {
    // to set aria expand true remaining
    parentCollapseDiv.classList.add("show");
    parentCollapseDiv.parentElement.children[0].classList.add("active");
    parentCollapseDiv.parentElement.children[0].setAttribute(
      "aria-expanded",
      "true"
    );
    if (parentCollapseDiv.parentElement.closest(".collapse.menu-dropdown")) {
      if (
        parentCollapseDiv.parentElement.closest(".collapse.menu-dropdown")
          .previousElementSibling
      ) {
        if (
          parentCollapseDiv.parentElement
            .closest(".collapse.menu-dropdown")
            .previousElementSibling.parentElement.closest(
              ".collapse.menu-dropdown"
            )
        ) {
          const grandparent = parentCollapseDiv.parentElement
            .closest(".collapse.menu-dropdown")
            .previousElementSibling.parentElement.closest(
              ".collapse.menu-dropdown"
            );
          this.activateIconSidebarActive("#" + grandparent.getAttribute("id"));
          grandparent.classList.add("show");
        }
      }
      this.activateIconSidebarActive(
        "#" +
          parentCollapseDiv.parentElement
            .closest(".collapse.menu-dropdown")
            .getAttribute("id")
      );

      parentCollapseDiv.parentElement
        .closest(".collapse")
        .classList.add("show");
      if (
        parentCollapseDiv.parentElement.closest(".collapse")
          .previousElementSibling
      )
        parentCollapseDiv.parentElement
          .closest(".collapse")
          .previousElementSibling.classList.add("active");
      return false;
    }
    this.activateIconSidebarActive("#" + parentCollapseDiv.getAttribute("id"));
    return false;
  }
  return false;
};

const activateIconSidebarActive = (id) => {
  var menu = document.querySelector(
    "#two-column-menu .simplebar-content-wrapper a[href='" + id + "'].nav-icon"
  );
  if (menu !== null) {
    menu.classList.add("active");
  }
};

const initActiveMenu = () => {
  const pathName = window.location.pathname;
  const ul = document.getElementById("navbar-nav");
  if (ul) {
    const items = Array.from(ul.querySelectorAll("a.nav-link"));
    let activeItems = items.filter((x) => x.classList.contains("active"));
    removeActivation(activeItems);
    let matchingMenuItem = items.find((x) => {
      return x.getAttribute("href") === pathName;
    });
    if (matchingMenuItem) {
      activateParentDropdown(matchingMenuItem);
    } else {
      var id = pathName.replace("/", "");
      if (id) document.body.classList.add("twocolumn-panel");
      activateIconSidebarActive(pathName);
    }
  }
};

onMounted(() => {
  initActiveMenu();

  const overlay = document.getElementById("overlay");
  if (overlay) {
    overlay.addEventListener("click", () => {
      document.body.classList.remove("vertical-sidebar-enable");
    });
  }

  // window.addEventListener("resize", () => {
  //   if (layoutType.value == 'twocolumn') {
  //     var windowSize = document.documentElement.clientWidth;
  //     if (windowSize < 767) {
  //       document.documentElement.setAttribute("data-layout", "vertical");
  //       localStorage.setItem('rmenu', 'vertical');
  //     } else {
  //       document.documentElement.setAttribute("data-layout", "vertical");
  //       localStorage.setItem('rmenu', 'twocolumn');
  //       setTimeout(() => {
  //         initActiveMenu();
  //       }, 50);

  //     }
  //   }
  // });

  if (document.querySelectorAll(".navbar-nav .collapse")) {
    let collapses = document.querySelectorAll(".navbar-nav .collapse");

    collapses.forEach((collapse) => {
      // Hide sibling collapses on `show.bs.collapse`
      collapse.addEventListener("show.bs.collapse", (e) => {
        e.stopPropagation();
        let closestCollapse = collapse.parentElement.closest(".collapse");
        if (closestCollapse) {
          let siblingCollapses = closestCollapse.querySelectorAll(".collapse");
          siblingCollapses.forEach((siblingCollapse) => {
            if (siblingCollapse.classList.contains("show")) {
              siblingCollapse.classList.remove("show");
              siblingCollapse.parentElement.firstChild.setAttribute(
                "aria-expanded",
                "false"
              );
            }
          });
        } else {
          let getSiblings = (elem) => {
            // Setup siblings array and get the first sibling
            let siblings = [];
            let sibling = elem.parentNode.firstChild;
            // Loop through each sibling and push to the array
            while (sibling) {
              if (sibling.nodeType === 1 && sibling !== elem) {
                siblings.push(sibling);
              }
              sibling = sibling.nextSibling;
            }
            return siblings;
          };
          let siblings = getSiblings(collapse.parentElement);
          siblings.forEach((item) => {
            if (item.childNodes.length > 2) {
              item.firstElementChild.setAttribute("aria-expanded", "false");
              item.firstElementChild.classList.remove("active");
            }
            let ids = item.querySelectorAll("*[id]");
            ids.forEach((item1) => {
              item1.classList.remove("show");
              item1.parentElement.firstChild.setAttribute(
                "aria-expanded",
                "false"
              );
              item1.parentElement.firstChild.classList.remove("active");
              if (item1.childNodes.length > 2) {
                let val = item1.querySelectorAll("ul li a");

                val.forEach((subitem) => {
                  if (subitem.hasAttribute("aria-expanded"))
                    subitem.setAttribute("aria-expanded", "false");
                });
              }
            });
          });
        }
      });

      // Hide nested collapses on `hide.bs.collapse`
      collapse.addEventListener("hide.bs.collapse", (e) => {
        e.stopPropagation();
        let childCollapses = collapse.querySelectorAll(".collapse");
        childCollapses.forEach((childCollapse) => {
          let childCollapseInstance = childCollapse;
          childCollapseInstance.classList.remove("show");
          childCollapseInstance.parentElement.firstChild.setAttribute(
            "aria-expanded",
            "false"
          );
        });
      });
    });
  }
});
</script>

<template>
  <BContainer fluid>
    <template v-if="layoutType === 'vertical'">
      <ul class="navbar-nav h-100" id="navbar-nav">
        <li class="menu-title">
          <span data-key="t-menu"> Dashboards </span>
        </li>
        <li class="nav-item">
          <router-link class="nav-link menu-link" to="/">
            <i class="ri-dashboard-2-line"></i>
            <span data-key="t-widgets">Main Dashboards</span>
          </router-link>
        </li>
        <li class="nav-item">
          <router-link class="nav-link menu-link" to="/ipc-management">
            <i class="ri-database-line"></i>
            <span data-key="t-widgets">IPC Management</span>
          </router-link>
        </li>
        <!-- <li class="nav-item">
          <router-link class="nav-link menu-link" to="/server-management">
            <i class="ri-server-line"></i>
            <span data-key="t-widgets">Server Management</span>
          </router-link>
        </li> -->
        <li class="nav-item">
          <router-link class="nav-link menu-link" to="/log-management">
            <i class="ri-file-text-line"></i>
            <span data-key="t-widgets">Log Management</span>
          </router-link>
        </li>
        <!-- <li class="nav-item">
          <router-link class="nav-link menu-link" to="/message-management">
            <i class="ri-message-2-line"></i>
            <span data-key="t-widgets">Message Management</span>
          </router-link>
        </li> -->
        <li class="nav-item">
          <router-link class="nav-link menu-link" to="/account-management">
            <i class="ri-account-circle-line"></i>
            <span data-key="t-widgets">Account Management</span>
          </router-link>
        </li>
      </ul>
    </template>
  </BContainer>
</template>
