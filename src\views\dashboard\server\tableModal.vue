<script setup>
import { reactive, ref, inject, onMounted } from "vue";
import { useToast } from "vue-toastification";
import { TabulatorFull as Tabulator } from "tabulator-tables";

import { getMessageData, updateMessageData } from "@/apis/e84_Api";
import { dataStore } from "@/store/modules/serverData";

const props = defineProps(["dataShow"]);
const toast = useToast();

const deviceId = ref(null);
const modalShow = ref(props.dataShow);

const refreshData = inject("refreshData");
const useDataStore = dataStore();

const editData = reactive({
  server: "",
  name: "",
});

let tableAlarmData = ref([]);
let tableAlarmRef = ref(null);
const tableAlarmTabulator = ref(null);
let alarmSelectedData = [];

let tableEventData = ref([]);
let tableEventRef = ref(null);
const tableEventTabulator = ref(null);
let eventSelectedData = [];

const showModal = (data) => {
  console.log("Data:", data);
  editData.name = data.name;
  getAlarmDataFunc();
  modalShow.value = true;
};

const tableAlarmTabulatorFunc = () => {
  try {
    tableAlarmTabulator.value = new Tabulator(tableAlarmRef.value, {
      placeholder: "No Data Available",
      data: tableAlarmData.value,
      autoResize: true,
      pagination: "remote",
      paginationSize: 10,
      rowHeight: 56,
      paginationSizeSelector: [10, 25, 50, 100],
      paginationCounter: "rows",
      layout: "fitColumns", //fitData, fitDataFill, fitDataTable, fitDataStretch, fitColumns
      reactiveData: true,
      dataLoader: true,
      height: 700,
      progressiveLoadDelay: 200,
      columns: [
        {
          title: "SF",
          field: "sf",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 100,
        },
        {
          title: "Code",
          field: "code",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 100,
        },
        {
          title: "Sub Code",
          field: "subcode",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 120,
        },
        {
          title: "Msg Text",
          field: "msgtext",
          headerHozAlign: "center",
          hozAlign: "left",
          vertAlign: "middle",
          // width: 150,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${cell.getValue()}</span>`;
          },
        },
        {
          title: "Description",
          field: "description",
          headerHozAlign: "center",
          hozAlign: "left",
          vertAlign: "middle",
          // width: 150,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${cell.getValue()}</span>`;
          },
        },
        {
          title: "Alarm / Event Setting",
          field: "webapi",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 180,
          formatter: function (cell) {
            return `<div class="form-check form-switch form-switch-right form-switch-md">
            <input class="form-check-input code-switcher" type="checkbox" role="switch" id="Switch${cell.getData().code}" value="${cell.getValue()}" ${cell.getValue() ? "checked" : ""}>
            </div>`;
          },
        },
      ],
    });

  } catch (err) {
    console.error(err);
  }
};

const getAlarmDataFunc = async () => {
  try {
    const data = {
      draw: 1,
      length: 20,
      start: 0,
      search: "",
      id: 0,
      start_id: 0,
      end_id: 0,
      start_time: "",
      end_time: "",
      order_by: "desc",
      order_column: "id",
      sf: "S05F01",
      code: -1,
      subcode: -1,
      msgtext: "",
      description: "",
      start2_time: "",
      end2_time: "",
    };

    const res = await getMessageData(data);
    console.log("Get Server:", res);

    console.log("Check Data", useDataStore.selectedDataIdx)

    if (res.status >= 200 && res.status < 300) {
      console.log("Message Data:", res.data);
      tableAlarmData.value = res.data.data;
      updateAlarmTableData(tableAlarmData.value);
    } else {
      toast.error(`Get Data Error`, {
        position: "bottom-right",
        timeout: 1000,
      });
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }
};

const refreshAlarmTable = () => {
  getAlarmDataFunc();
};

const updateAlarmTableData = (newData) => {
  tableAlarmTabulator.value.replaceData(newData);
  tableAlarmTabulator.value.selectRow(alarmSelectedData.value);
};

const editAlarmFunc = async () => {
  console.log("Edit Device:", deviceId.value);
  try {
    const data = {
      name: editData.name,
      url: editData.url,
      ip: editData.ip,
      port: editData.port,
      svr_enable: editData.svr_enable,
    };
    console.log("EDIT DATA:", data);

    const res = await updateMessageData(data);
    console.log("Edit Server:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Edit Server ${editData.name} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData();
    }
    if (res.status >= 400) {
      toast.error(`Edit Server ${editData.name} Error`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData();
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

const tableEventTabulatorFunc = () => {
  try {
    tableEventTabulator.value = new Tabulator(tableEventRef.value, {
      placeholder: "No Data Available",
      data: tableEventData.value,
      autoResize: true,
      pagination: "remote",
      paginationSize: 10,
      rowHeight: 56,
      paginationSizeSelector: [10, 25, 50, 100],
      paginationCounter: "rows",
      layout: "fitColumns", //fitData, fitDataFill, fitDataTable, fitDataStretch, fitColumns
      reactiveData: true,
      dataLoader: true,
      height: 700,
      progressiveLoadDelay: 200,
      columns: [
        {
          title: "SF",
          field: "sf",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 100,
        },
        {
          title: "Code",
          field: "code",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 100,
        },
        {
          title: "Sub Code",
          field: "subcode",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 120,
        },
        {
          title: "Msg Text",
          field: "msgtext",
          headerHozAlign: "center",
          hozAlign: "left",
          vertAlign: "middle",
          // width: 150,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${cell.getValue()}</span>`;
          },
        },
        {
          title: "Description",
          field: "description",
          headerHozAlign: "center",
          hozAlign: "left",
          vertAlign: "middle",
          // width: 150,
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${cell.getValue()}</span>`;
          },
        },
        {
          title: "Alarm / Event Setting",
          field: "",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          width: 180,
          formatter: function (cell) {
            return `<div class="form-check form-switch form-switch-right form-switch-md">
            <input class="form-check-input code-switcher" type="checkbox" role="switch" id="Switch${cell.getData().code}" ${cell.getData().webapi ? "checked" : ""}>
            </div>`;
          },
        },
      ],
    });
  } catch (err) {
    console.error(err);
  }
};

const getEventDataFunc = async () => {
  try {
    const data = {
      draw: 1,
      length: 20,
      start: 0,
      search: "",
      id: 0,
      start_id: 0,
      end_id: 0,
      start_time: "",
      end_time: "",
      order_by: "desc",
      order_column: "id",
      sf: "S06F11",
      code: -1,
      subcode: -1,
      msgtext: "",
      description: "",
      start2_time: "",
      end2_time: "",
    };

    const res = await getMessageData(data);
    console.log("Get Event:", res);

    if (res.status >= 200 && res.status < 300) {
      console.log("Event Data:", res.data);
      tableEventData.value = res.data.data;
      updateEventTableData(tableEventData.value);
    } else {
      toast.error(`Get Event Data Error`, {
        position: "bottom-right",
        timeout: 1000,
      });
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error (
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }
};

const refreshEventTable = () => {
  getEventDataFunc();
};

const updateEventTableData = (newData) => {
  tableEventTabulator.value.replaceData(newData);
  tableEventTabulator.value.selectRow(eventSelectedData.value);
};

const editEventFunc = async () => {
  console.log("Edit Device:", deviceId.value);
  try {
    const data = {
      name: editData.name,
      url: editData.url,
      ip: editData.ip,
      port: editData.port,
      svr_enable: editData.svr_enable,
    };
    console.log("EDIT DATA:", data);

    const res = await updateMessageData(data);
    console.log("Edit Server:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Edit Server ${editData.name} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData();
    }
    if (res.status >= 400) {
      toast.error(`Edit Server ${editData.name} Error`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData();
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

defineExpose({ showModal });

onMounted(() => {
  tableAlarmTabulatorFunc();
  tableEventTabulatorFunc();
});
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom"
    size="xl"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body p-0">
      <BTabs nav-class="mb-3" pills justified>
        <BTab title="Alarm" active @click="refreshAlarmTable">
          <div class="text-muted">
            <div class="w-100">
              <div
                id="tableAlarmTabulator"
                ref="tableAlarmRef"
                class="mb-2"
              ></div>
            </div>
          </div>
          <div class="modal-footer v-modal-footer border-top">
            <div class="mx-auto">
              <BButton
                type="submit"
                variant="primary"
                class="me-3"
                @click="editAlarmFunc"
                >Save</BButton
              >
            </div>
          </div>
        </BTab>
        <BTab title="Event" @click="refreshEventTable">
          <div class="text-muted">
            <div class="w-100">
              <div
                id="tableEventTabulator"
                ref="tableEventRef"
                class="mb-2"
              ></div>
            </div>
          </div>
          <div class="modal-footer v-modal-footer border-top">
            <div class="mx-auto">
              <BButton
                type="submit"
                variant="primary"
                class="me-3"
                @click="editEventFunc"
                >Save</BButton
              >
            </div>
          </div>
        </BTab>
      </BTabs>
    </div>
  </BModal>
</template>
