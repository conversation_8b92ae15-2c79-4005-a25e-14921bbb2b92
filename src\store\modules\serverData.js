import { defineStore } from 'pinia'

export const dataStore = defineStore('dataStore', {
    state: () => ({
        selectedDataIdx: [],
        type: ''
    }),
    actions: {
        selectData(data) {
            if (!this.isSelected(data.idx)) {
                console.log("CHECK IS SELECTED:", this.isSelected(data))
                this.selectedDataIdx.push(data);
            }
        },
        deSelectData(dataId) {
            console.log("DESELECT DATA ID:",  dataId);
            console.log("SELECT DATA IDX:",  this.selectedDataIdx);
            const index = this.selectedDataIdx.findIndex(data => data.idx === dataId.idx);
            console.log("DESELECT DATA INDEX:",  index);
            if (index !== -1) {
                this.selectedDataIdx.splice(index, 1);
            } else {
                console.warn('Data is not selected:', dataId);
            }
        },
        updateSelectedFlag(dataId, isSelected) {
            const dataToUpdate = this.selectedDataIdx.find(data => data.idx === dataId);
            if (dataToUpdate) {
                dataToUpdate.selected = isSelected;
            }
        },
        isSelected(dataId) {
            console.log("DATA ID:", dataId);
            const isSelected = this.selectedDataIdx.some(data => data.idx === dataId);
            console.log("IS SELECTED:", isSelected);
            return isSelected;
        },
        clearSelect() {
            this.selectedDataIdx = []
        },
        openModalType(type) {
            console.log("GET MODAL TYPE", type)
            this.type = type
        }
    }
})