<script setup>
import { computed, reactive, ref, watch, inject } from "vue";
import { useToast } from "vue-toastification";
import { addIpcDevice } from "@/apis/e84_Api";

const props = defineProps(["modalShow"]);
const toast = useToast();

const deviceId = ref(null);
const modalShow = ref(props.dataShow);

const refreshData = inject('refreshData')

const addData = reactive({
  deviceId: "",
  name: "",
  group: "",
  ip: "",
  port: "",
  ipcEnable: true,
});

const computedPort1 = computed(() => {
  return `${addData.deviceId}_LP1`
});

const computedPort2 = computed(() => {
  return `${addData.deviceId}_LP2`
});

const computedPort3 = computed(() => {
  return `${addData.deviceId}_LP3`
});

const computedPort4 = computed(() => {
  return `${addData.deviceId}_LP4`
});


const showModal = (data) => {
  // console.log("Device ID:", data);
  deviceId.value = data;
  modalShow.value = true;
};


const resetForm = () => {
      addData.deviceId = ""
      addData.name = ""
      addData.group = ""
      addData.ip = ""
      addData.port = ""
      addData.ipcEnable = true
    }

const addIPCFunc = async () => {
  console.log("Add Device:", deviceId.value);
  try {
    const data = {
      device_id: addData.deviceId,
      name: addData.name,
      group: addData.group,
      ip: addData.ip,
      port: addData.port,
      ipc_enable: addData.ipcEnable,
      ports: [
        {
          port_id: computedPort1.value,
          port_no: 1,
          dual_port: 0
        },
        {
          port_id: computedPort2.value,
          port_no: 2,
          dual_port: 0
        },
        {
          port_id: computedPort3.value,
          port_no: 3,
          dual_port: 0
        },
        {
          port_id: computedPort4.value,
          port_no: 4,
          dual_port: 0
        }
      ]
    };
    console.log("ADD DATA:", data);

    const res = await addIpcDevice(data);
    console.log("Add Device:", res);

    if (res.status >= 200 && res.status < 300) {
      toast.success(`Add Device ${addData.deviceId} Success`, {
        position: "bottom-right",
        timeout: 1000,
      });
      refreshData()
    }
  } catch (error) {
    console.log("ERROR", error);
    toast.error(
      `ERROR ${error.response.status}: Get API ${error.response.statusText}. [ Detail: ${error.response.data.detail} ]`,
      {
        position: "bottom-right",
        timeout: 1000,
      }
    );
  }

  modalShow.value = false;
};

watch(
  () => modalShow.value,
  (newValue) => {
    if (newValue === false) {
      resetForm();
    }
  }
);

defineExpose({ showModal });
</script>
<template>
  <BModal
    v-model="modalShow"
    hide-footer
    class="v-modal-custom modal-lg"
    title="Add IPC Device"
    centered
    no-close-on-backdrop
  >
    <div class="modal-body pt-0">
      <BRow class="g-3 mb-3">
        <BCol xxl="6">
          <div>
            <label for="deviceId" class="form-label">Device ID</label>
            <input
              v-model="addData.deviceId"
              type="text"
              class="form-control"
              id="deviceId"
              placeholder="Enter Device ID"
            />
          </div>
        </BCol>
        <BCol xxl="6">
          <div>
            <label for="name" class="form-label">Name</label>
            <input
              v-model="addData.name"
              type="text"
              class="form-control"
              id="name"
              placeholder="Enter name"
            />
          </div>
        </BCol>
        <BCol xxl="4">
          <label for="group" class="form-label">Group</label>
          <input
            v-model="addData.group"
            type="text"
            class="form-control"
            id="group"
            placeholder="Enter IPC Group"
          />
        </BCol>
        <BCol xxl="5">
          <label for="ip" class="form-label">IP</label>
          <input
            v-model="addData.ip"
            type="text"
            class="form-control"
            id="ip"
            placeholder="Enter IPC IP Address"
          />
        </BCol>
        <BCol xxl="3">
          <label for="port" class="form-label">Port</label>
          <input
            v-model="addData.port"
            type="text"
            class="form-control"
            id="port"
            placeholder="Enter IPC Port"
          />
        </BCol>
      </BRow>
      <BRow class="g-3">
        <BCol xxl="6">
          <label for="port1" class="form-label">Loadport 1</label>
            <input
              v-model="computedPort1"
              type="text"
              class="form-control"
              id="port1"
              placeholder="Enter IPC Loadport 1"
            />  
        </BCol>
        <BCol xxl="6">
          <label for="port2" class="form-label">Loadport 2</label>
            <input
              v-model="computedPort2"
              type="text"
              class="form-control"
              id="port2"
              placeholder="Enter IPC Loadport 2"
            />  
        </BCol>
        <BCol xxl="6">
          <label for="port3" class="form-label">Loadport 3</label>
            <input
              v-model="computedPort3"
              type="text"
              class="form-control"
              id="port3"
              placeholder="Enter IPC Loadport 3"
            />  
        </BCol>
        <BCol xxl="6">
          <label for="port4" class="form-label">Loadport 4</label>
            <input
              v-model="computedPort4"
              type="text"
              class="form-control"
              id="port4"
              placeholder="Enter IPC Loadport 4"
            />  
        </BCol>
      </BRow>
      <BRow class="mt-3">
        <BCol xxl="6">
          <BFormGroup class="form-check-right w-100 ps-0 pe-5">
            <BFormCheckbox
              id="ipcEnableSwitch"
              v-model="addData.ipcEnable"
              switch
              class="form-switch-md me-2"
            >
              IPC Enable
            </BFormCheckbox>
          </BFormGroup>
        </BCol>
      </BRow>
    </div>
    <div class="modal-footer v-modal-footer border-top">
      <div class="mx-auto">
        <BButton type="submit" variant="primary" class="me-3" @click="addIPCFunc" :disabled="!addData.deviceId || !addData.name || !addData.group || !addData.ip || !addData.port"
          >Submit</BButton
        >
        <BButton type="button" variant="light" @click="modalShow = false"
          >Cancel
        </BButton>
      </div>
    </div>
  </BModal>
</template>
