<script setup>
import { ref } from "vue";
const props = defineProps(["title"]);

const title = ref(props.title);

const searchQuery = ref("");

</script>

<template>
  <BCardHeader>
    <BRow class="g-4 align-items-center">
      <BCol sm="auto">
        <div>
          <BCardTitle class="mb-0 flex-grow-1">{{ title }}</BCardTitle>
        </div>
      </BCol>
      <BCol sm>
        <div class="d-flex justify-content-sm-end">
          <div class="search-box ms-2">
            <input
              type="text"
              class="form-control"
              id="searchResultList"
              placeholder="Search ..."
              v-model="searchQuery"
            />
            <i class="ri-search-line search-icon"></i>
          </div>
        </div>
      </BCol>
    </BRow>
  </BCardHeader>
</template>
