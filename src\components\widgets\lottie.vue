<template>
  <div :style="style" ref="lavContainer"></div>
</template>

<script setup>
import { ref, defineProps, defineEmits, reactive, onMounted } from 'vue';
import lottie from 'lottie-web';

const props = defineProps({
  options: {
      type: Object,
      default: () => ({})
  },
  height: {
      type: Number,
      default: 0
  },
  width: {
      type: Number,
      default: 0
  }
});

const emit = defineEmits(['animCreated']);

const style = reactive({
  width: props.width ? `${props.width}px` : '100%',
  height: props.height ? `${props.height}px` : '100%',
  overflow: 'hidden',
  margin: '0 auto',
});

let anim;
const lavContainer = ref();

onMounted(() => {
  anim = lottie.loadAnimation({
      container: lavContainer.value,
      renderer: 'svg',
      loop: props.options.loop !== false,
      autoplay: props.options.autoplay !== false,
      animationData: props.options.animationData,
      rendererSettings: props.options.rendererSettings
  });
  emit('animCreated', anim);
});

</script>