<script setup>
import {  ref, onMounted, reactive } from "vue";
// import { useToast } from "vue-toastification";
import { TabulatorFull as Tabulator } from "tabulator-tables";
import dayjs from 'dayjs'
import { getIpcList, getIpcFileDownload } from "@/apis/e84_Api";

const formatDate = (isoString) => {
  return dayjs(isoString).format('YYYY/MM/DD HH:mm:ss')
}
// const toast = useToast();

// let tableData = ref([]);
let tableRef = ref(null);
const tableTabulator = ref(null);

const searchQuery = ref("");
const searchKeywords = ref("");

const serverSelected = ref("");
let serverOptions = ref([
  { value: "", text: "Server" },
]);

const date = ref();
const startDate = ref();
const endDate = ref();

const getIpcListData = async () => {
  const data = {
        draw: 1,
        length: 10,
        start: 0,
        device_id: "",
        search: "",
        start_datetime: "",
        end_datetime: ""
}

  const res = await getIpcList(data);
  console.log("IPC List Data:", res.data);
  
  res.data.data.map((item) => {
    console.log("Item:", item)
    let object = {
      value: item.device_id,
      text: `${item.group} - ${item.device_id} - ${item.name}`
    }
    serverOptions.value.push(object);
  });  
};


const urlData = JSON.parse(localStorage.getItem("serverConfig"));
const token = localStorage.getItem("jwt");
const basicUrl = "http://" + urlData.E84.ip_address;
const apiUrl = "/api/log/files/list";

const filterData = reactive({
  order_by: "desc",
  order_column: "id",
  search: "",
  start_time: date.value,
  end_time: endDate.value,
  device_id: ""
});

const formatDateTime = (dateTime) => {
  const date = dayjs(dateTime).format("YYYY-MM-DD HH:mm");
  return date
}


const tableTabulatorFunc = () => {
  try {
    tableTabulator.value = new Tabulator(tableRef.value, {
      placeholder: "No Data Available",
      dataLoaderLoading: `<span class="spinner-border flex-shrink-0" role="status">
      <span class="visually-hidden">Loading...</span>
    </span>`,
      ajaxURL: basicUrl + apiUrl,
      ajaxConfig: "POST",
      ajaxContentType: {
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + token,
        },
        body: function (url, config, params) {
          console.log("CONTENT TYPE HEADER PARAMS", params)

          params.draw = 1
          params.start = (params.start - 1) * params.length;
          params.order_by = filterData.order_by;
          params.order_column = filterData.order_column;

          params.search = searchKeywords.value ? searchKeywords.value.trim() : "";
          params.start_datetime = filterData.start_time;
          params.end_datetime = filterData.end_time;
          params.device_id = filterData.device_id
          
          console.log("CONTENT TYPE HEADER PARAMS END", params)
          return JSON.stringify(params);
        },
      },
      dataSendParams: {
        page: "start",
        size: "length",
      },
      dataReceiveParams: {
        last_page: "recordsTotal",
      },
      pagination: true,
      paginationMode: "remote",
      paginationCounter: "rows",
      paginationSize: 10,
      selectable: false,
      paginationSizeSelector: [10, 25, 50, 100],
      ajaxResponse: function (url, params, response) {
        let nowPageSize = params.length;
        let totalPage = response.recordsTotal / nowPageSize;
        console.log("API DATA RES", response);

        return {
          data: response.data,
          last_page: Math.ceil(totalPage),
          last_row: response.recordsTotal,
        };
      },
      autoResize: false,
      rowHeight: 46,
      layout: "fitColumns",
      reactiveData: true,
      height: 595,
      columns: [
        {
          title: "File Name",
          field: "filename",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
        },
        {
          title: "Extension",
          field: "extension",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
        },
        {
          title: "Modified",
          field: "modified",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          formatter: function (cell) {
            return `<span class="mx-2 text-wrap lh-1">${formatDate(cell.getValue())}</span>`;
          },

        },
        {
          title: "Size",
          field: "size",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
        },
        {
          title: "Action",
          field: "",
          headerHozAlign: "center",
          hozAlign: "center",
          vertAlign: "middle",
          formatter: function (cell) {
            const downBtn = document.createElement("button");
            downBtn.classList.add(
              "btn",
              "btn-primary",
              "btn-block",
              "d-flex",
              "align-items-center",
              "justify-content-center"
            );
            downBtn.innerHTML = `Download`
            downBtn.setAttribute("type", "button");
            downBtn.addEventListener("click", function (event) {
              event.stopPropagation();
              // console.log("Download Clicked");
              downloadFile(cell.getData().filename, cell.getData().filepath);
            });

            return downBtn;
          },
        },
      ],
    });
  } catch (err) {
    console.error(err);
  }
};


const refreshTable = () => {
  searchQuery.value = "";
  // tableTabulator.value.destroy();
  // tableTabulatorFunc();
  tableTabulator.value.setData();
};

const resetQuery = () => {
  searchKeywords.value = "";
  tableTabulator.value.destroy();
  tableTabulatorFunc();
};

const downloadFile = async (filename, filepath) => {
  console.log("Download File:", filename, filepath);

  const data = {
    device_id: "",
    filename: filename,
    filepath: filepath
  }

  // console.log("Download File Data:", data);

  const item = await getIpcFileDownload(data)

  // console.log("Download File:", item);
  const content = item.data.replace(/\r\n/g, '\n');


  const blob = new Blob([content], { type: 'text/plain' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}.txt`;

  link.click();
  window.URL.revokeObjectURL(url);
};

// const updateAlarmTableData = (newData) => {
//   tableAlarmTabulator.value.replaceData(newData);
//   tableAlarmTabulator.value.selectRow(alarmSelectedData.value);
// };

// const onKeyUpSearch = event => {
//   if (event.key === 'Enter' && Object.values(searchQuery).filter(value => value !== '').length > 0) {
//     searchKeywords.value = searchQuery.value;
//     refreshTable();    
//   }
// };

// const getTodayData = async () => {

//   let dateTime = "2024-02-22T14:57:06";
// let date = new Date(dateTime);

// // 获取开始时间 (00:00:00)
// let startOfDay = new Date(date); // Initially set it to the given date
// startOfDay.setHours(0, 0, 0, 0); // Set the time to start of the day

// // 获取结束时间 (23:59:59)
// let endOfDay = new Date(date); // Initially set it to the given date
// endOfDay.setDate(endOfDay.getDate() + 1); // Add a day
// endOfDay.setHours(0, 0, 0, 0); // Set the time to end of the day

// // let formatDateTime = (dateTime) => {
// //   let yyyy = dateTime.getFullYear();
// //   let mm = String(dateTime.getMonth() + 1).padStart(2, '0');
// //   let dd = String(dateTime.getDate()).padStart(2, '0');
// //   let hh = String(dateTime.getHours()).padStart(2, '0');
// //   let min = String(dateTime.getMinutes()).padStart(2, '0');
// //   return `${yyyy}-${mm}-${dd} ${hh}:${min}`;
// // }

//     // let data = {
//     //   draw: 1,
//     //   order_by: "desc",
//     //   order_column: "id",
//     //   search: "",
//     //   id: 0,
//     //   start_id: 0,
//     //   end_id: 0,
//     //   start_time: formatDateTime(startOfDay),
//     //   end_time: formatDateTime(endOfDay),
//     //   length: 5000,
//     //   start: 0,
//     //   device_id: "",
//     //   dataid: 0,
//     //   ceid: 0,
//     //   rpt: "",
//     //   cause: "",
//     //   detail: "",
//     //   description: "",
//     //   start2_time: "",
//     //   end2_time: ""
//     // };

//     // console.log("Post Data:", data)

//     // const res = await getEventList(data);
    
//     // console.log("Today Data:", res.data);

//     // return res.data
// };

// const downloadTableFunc = () => {
//         downloadTable.value = new Tabulator(downloadTableRef.value, {
//             data: downloadData.value,
//             columns: [
//             {
//           title: "ID",
//           field: "id",
//         },
//         {
//           title: "Device ID",
//           field: "device_id",
//         },
//         {
//           title: "Data ID",
//           field: "dataid",
//         },
//         {
//           title: "CE ID",
//           field: "ceid",
//         },
//         {
//           title: "RPT",
//           field: "rpt",
//         },
//         {
//           title: "Created At",
//           field: "created_at",
//         },
//         {
//           title: "Updated At",
//           field: "updated_at",
//         },
//             ],
//         });
// }

// const exportCsv = async () => {
//   downloadData.value = await getTodayData();
//   let timeStamp = `${new Date().getFullYear()}${(new Date().getMonth()+1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}_${new Date().getHours().toString().padStart(2, '0')}${new Date().getMinutes().toString().padStart(2, '0')}`;

//         let fileName = 'EventLogs_'+ timeStamp +'.csv'

//         console.log("CSV Data:", fileName, downloadData.value);
//         downloadTable.value.setData(downloadData.value);

//         downloadTable.value.download("csv", fileName);
// };



// defineExpose({ showModal, refreshTable});
defineExpose({ getIpcListData, refreshTable });

onMounted(() => {
  startDate.value = formatDateTime(new Date());
  endDate.value = "";
  date.value = [startDate.value, endDate.value];

  tableTabulatorFunc();
  // downloadTableFunc();

});
</script>
<template>
  <BCard no-body>
      <BCardHeader class="">
        <BRow class="g-4">
          <BCol lg="3">
            <BFormSelect v-model="serverSelected" class="mb-3" aria-label="Server select" :options="serverOptions"></BFormSelect>
          </BCol>
          <BCol lg="4">
            <VueDatePicker v-model="date" range multi-calendars />
          </BCol>
          <BCol sm>
            <div class="d-flex justify-content-sm-end">
              <BButton
                type="button"
                class="me-2"
                variant="success"
                v-throttle="refreshTable"
                :delay="300"
              >
                <i class="ri-refresh-line align-middle me-1"></i>
                Refresh
              </BButton>
              <BButton
                type="button"
                class="me-2"
                variant="success"
                v-throttle="resetQuery"
                :delay="300"
              >
                Reset
              </BButton>
              <div class="search-box ms-2">
                <input
                  type="text"
                  class="form-control"
                  id="searchResultList"
                  placeholder="Search ..."
                  v-model="searchQuery"
                  @keyup="onKeyUpSearch"
                  on
                />
                <i class="ri-search-line search-icon"></i>
              </div>
            </div>
          </BCol>
        </BRow>
      </BCardHeader>
      <BCardBody>
        <div class="table-responsive table-card position-relative vl-parent">
          <div id="tableTabulator" ref="tableRef" class="mb-2"></div>
          <!-- <div class="d-none" id="downloadTabulator" ref="downloadTableRef"></div> -->
        </div>
      </BCardBody>
    </BCard>
</template>
<style>
.tabulator .tabulator-alert .tabulator-alert-msg.tabulator-alert-state-msg {
  border: 0px solid #000000 !important;
  background-color: rgba(0, 0, 0, 0) !important;
}
</style>