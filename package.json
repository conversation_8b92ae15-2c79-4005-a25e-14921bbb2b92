{"name": "basic-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@fullcalendar/bootstrap": "^6.1.10", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/list": "^6.1.10", "@fullcalendar/multimonth": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/vue3": "^6.1.10", "@popperjs/core": "^2.11.8", "@vueform/multiselect": "^2.6.6", "@vueform/slider": "^2.1.10", "@vueform/toggle": "^2.1.4", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vuepic/vue-datepicker": "^8.5.0", "@vueuse/core": "^10.7.2", "@vueuse/integrations": "^10.7.2", "@zhuowenli/vue-feather-icons": "^5.0.2", "aos": "^2.3.4", "apexcharts": "^3.45.2", "axios": "^1.6.5", "bootstrap": "^5.3.2", "bootstrap-vue-next": "^0.15.5", "click-outside-vue3": "^4.0.1", "core-js": "^3.35.1", "dayjs": "^1.11.10", "default-passive-events": "^2.0.0", "echarts": "^5.4.3", "element-plus": "^2.6.0", "feather-icons": "^4.29.1", "highlight.js": "^11.9.0", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "maska": "^2.1.11", "mitt": "^3.0.1", "node-sass": "^9.0.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "prismjs": "^1.29.0", "sass-loader": "^14.0.0", "simplebar": "^6.2.5", "simplebar-vue": "^2.3.3", "sweetalert2": "^11.10.3", "swiper": "^11.0.5", "tabulator-tables": "^5.5.4", "universal-cookie": "^7.0.2", "vite-plugin-eslint": "^1.8.1", "vue": "^3.3.11", "vue-draggable-next": "^2.2.1", "vue-feather": "^2.0.0", "vue-flatpickr-component": "^11.0.3", "vue-i18n": "^9.9.0", "vue-loading-overlay": "^6.0.4", "vue-prismjs": "^1.2.0", "vue-router": "^4.2.5", "vue-toastification": "2.0.0-rc.5", "vue3-count-to": "^1.1.2", "vue3-echarts": "^1.1.0"}, "devDependencies": {"@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@eslint/js": "^8.56.0", "@vitejs/plugin-vue": "^4.6.2", "autoprefixer": "^10.4.17", "cz-git": "^1.8.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.20.1", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^15.2.0", "postcss": "^8.4.33", "prettier": "^3.2.4", "sass": "^1.69.0", "sass-loader": "^13.3.2", "stylelint": "^16.2.0", "unplugin-auto-import": "^0.17.3", "unplugin-icons": "^0.18.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.8", "vue-eslint-parser": "^9.4.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"], "*.{html,sass,scss,less}": ["prettier --write", "stylelint --fix"], "package.json": ["prettier --write"], "*.md": ["prettier --write"]}, "resolutions": {"autoprefixer": "10.4.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "rules": {"vue/multi-word-component-names": "off"}}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "engines": {"node": "^18.0.0 || >=20.0.0", "pnpm": ">=8.6.10"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}