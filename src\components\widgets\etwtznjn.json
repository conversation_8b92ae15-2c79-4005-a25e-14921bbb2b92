{"v": "5.8.1", "fr": 60, "ip": 0, "op": 181, "w": 500, "h": 500, "nm": "1865-shooting-stars-outline", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "watermark", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11, "x": "var $bm_rt;\nvar checkbox = thisComp.layer('02092020').effect('02092020002')('Checkbox');\nif (checkbox == 1) {\n    $bm_rt = 20;\n} else {\n    $bm_rt = 0;\n}\n;"}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [249.934, 481.369, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [79.934, 0.369, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [265.159, 265.159, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.415, 0], [11.014, 0], [11.014, -2.523], [4.656, -2.523], [4.656, -14.809], [1.415, -14.809]], "c": true}, "ix": 2}, "nm": "l", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, -3.938], [-1.62, -1.723], [-1.949, 0], [-1.641, 1.846], [0, 2.154], [1.579, 1.805], [1.579, 0]], "o": [[0, 1.354], [1.354, 1.415], [1.231, 0], [1.21, -1.354], [0, -1.456], [-1.456, -1.641], [-5.333, 0]], "v": [[11.167, -7.199], [12.992, -1.661], [18.243, 0.369], [23.514, -1.743], [25.381, -7.548], [23.494, -13.127], [18.284, -15.137]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 1.415], [-0.841, 1.026], [-1.19, 0], [-0.615, -1.825], [0, -0.718], [0.492, -0.738], [1.292, 0], [0.451, 0.615]], "o": [[0, -1.682], [0.595, -0.759], [1.518, 0], [0.308, 0.902], [0, 2.359], [-0.595, 0.923], [-1.477, 0], [-0.882, -1.149]], "v": [[14.49, -7.302], [15.577, -11.609], [18.305, -12.86], [21.689, -10.235], [22.058, -7.589], [21.053, -3.343], [18.284, -1.969], [15.597, -3.159]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.287, -0.841], [-0.144, -0.82], [0, 0], [0.164, 0.656], [0.226, 1.743], [2.236, 0.205], [0, 2.769], [0.923, 0.8], [1.641, -0.021], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.533, 0], [0.205, 0.574], [0, 0], [-0.164, -0.246], [-0.103, -0.41], [-0.267, -1.928], [0.718, -0.205], [0, -0.964], [-1.19, -1.026], [0, 0], [0, 0]], "v": [[27.381, 0], [30.622, 0], [30.622, -5.989], [33.411, -5.989], [35.011, -5.148], [35.811, 0], [39.318, 0], [38.867, -1.067], [38.416, -3.938], [35.749, -7.343], [38.847, -10.973], [37.554, -13.824], [33.063, -14.829], [27.381, -14.829]], "c": true}, "ix": 2}, "nm": "r", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.492, -0.349], [0, -1.005], [0.226, -0.164], [0.369, 0], [0, 0]], "o": [[0, 0], [1.005, 0], [0.287, 0.185], [0, 1.046], [-0.513, 0.41], [0, 0], [0, 0]], "v": [[30.519, -12.491], [32.652, -12.491], [34.744, -12.142], [35.524, -10.481], [34.703, -8.758], [33.083, -8.348], [30.519, -8.348]], "c": true}, "ix": 2}, "nm": "r", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.554, 0.103], [0, 4.553], [1.866, 1.374], [0.82, 0], [0, 0]], "o": [[0, 0], [1.497, 0], [2.81, -0.513], [0, -2.113], [-1.784, -1.313], [0, 0], [0, 0]], "v": [[41.068, 0], [45.683, 0], [48.349, -0.164], [53.6, -7.609], [51.077, -13.434], [45.97, -14.768], [41.068, -14.788]], "c": true}, "ix": 2}, "nm": "d", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.656, -0.185], [0, -2.092], [1.251, -1.251], [1.354, 0], [0.349, 0.021]], "o": [[1.825, -0.082], [1.99, 0.554], [0, 0.718], [-0.923, 0.923], [-0.369, 0], [0, 0]], "v": [[44.288, -12.388], [47.611, -12.183], [50.318, -7.609], [48.985, -3.425], [45.539, -2.4], [44.288, -2.441]], "c": true}, "ix": 2}, "nm": "d", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[55.669, 0], [58.849, 0], [58.849, -14.87], [55.669, -14.87]], "c": true}, "ix": 2}, "nm": "i", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [3.241, 0], [0, -4.697], [-5.107, 0], [-1.313, 1.354], [-0.062, 0.882], [0, 0], [1.333, 0], [0, 0.882], [-2.359, 0], [-0.062, -0.513]], "o": [[0, -2.954], [-4.164, 0], [0, 3.671], [1.354, 0], [1.19, -1.231], [0, 0], [-0.062, 1.969], [-3.097, 0], [0, -3.056], [2.154, 0], [0, 0]], "v": [[73.104, -9.989], [67.587, -14.911], [60.798, -7.097], [67.566, 0.349], [71.894, -1.313], [73.227, -4.799], [69.884, -4.799], [67.218, -1.99], [64.121, -7.076], [67.464, -12.593], [69.864, -9.989]], "c": true}, "ix": 2}, "nm": "c", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 9, "ty": "sh", "ix": 10, "ks": {"a": 0, "k": {"i": [[0, -3.938], [-1.62, -1.723], [-1.949, 0], [-1.641, 1.846], [0, 2.154], [1.579, 1.805], [1.579, 0]], "o": [[0, 1.354], [1.354, 1.415], [1.231, 0], [1.21, -1.354], [0, -1.456], [-1.456, -1.641], [-5.333, 0]], "v": [[74.546, -7.199], [76.372, -1.661], [81.622, 0.369], [86.894, -1.743], [88.76, -7.548], [86.873, -13.127], [81.663, -15.137]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 10, "ty": "sh", "ix": 11, "ks": {"a": 0, "k": {"i": [[0, 1.415], [-0.841, 1.026], [-1.19, 0], [-0.615, -1.825], [0, -0.718], [0.492, -0.738], [1.292, 0], [0.451, 0.615]], "o": [[0, -1.682], [0.595, -0.759], [1.518, 0], [0.308, 0.902], [0, 2.359], [-0.595, 0.923], [-1.477, 0], [-0.882, -1.149]], "v": [[77.869, -7.302], [78.956, -11.609], [81.684, -12.86], [85.068, -10.235], [85.437, -7.589], [84.432, -3.343], [81.663, -1.969], [78.977, -3.159]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 11, "ty": "sh", "ix": 12, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[91.007, 0], [94.001, 0], [94.001, -12.306], [99.744, 0], [104.113, 0], [104.113, -14.829], [101.159, -14.829], [101.159, -3.159], [95.601, -14.829], [91.007, -14.829]], "c": true}, "ix": 2}, "nm": "n", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 12, "ty": "sh", "ix": 13, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[106.893, 0], [109.497, 0], [109.497, -2.728], [106.893, -2.728]], "c": true}, "ix": 2}, "nm": ".", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 13, "ty": "sh", "ix": 14, "ks": {"a": 0, "k": {"i": [[0, 0], [3.241, 0], [0, -4.697], [-5.107, 0], [-1.313, 1.354], [-0.062, 0.882], [0, 0], [1.333, 0], [0, 0.882], [-2.359, 0], [-0.062, -0.513]], "o": [[0, -2.954], [-4.164, 0], [0, 3.671], [1.354, 0], [1.19, -1.231], [0, 0], [-0.062, 1.969], [-3.097, 0], [0, -3.056], [2.154, 0], [0, 0]], "v": [[124.04, -9.989], [118.523, -14.911], [111.734, -7.097], [118.502, 0.349], [122.83, -1.313], [124.163, -4.799], [120.82, -4.799], [118.154, -1.99], [115.057, -7.076], [118.4, -12.593], [120.8, -9.989]], "c": true}, "ix": 2}, "nm": "c", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 14, "ty": "sh", "ix": 15, "ks": {"a": 0, "k": {"i": [[0, -3.938], [-1.62, -1.723], [-1.949, 0], [-1.641, 1.846], [0, 2.154], [1.579, 1.805], [1.579, 0]], "o": [[0, 1.354], [1.354, 1.415], [1.231, 0], [1.21, -1.354], [0, -1.456], [-1.456, -1.641], [-5.333, 0]], "v": [[125.482, -7.199], [127.308, -1.661], [132.558, 0.369], [137.829, -1.743], [139.696, -7.548], [137.809, -13.127], [132.599, -15.137]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 15, "ty": "sh", "ix": 16, "ks": {"a": 0, "k": {"i": [[0, 1.415], [-0.841, 1.026], [-1.19, 0], [-0.615, -1.825], [0, -0.718], [0.492, -0.738], [1.292, 0], [0.451, 0.615]], "o": [[0, -1.682], [0.595, -0.759], [1.518, 0], [0.308, 0.902], [0, 2.359], [-0.595, 0.923], [-1.477, 0], [-0.882, -1.149]], "v": [[128.805, -7.302], [129.892, -11.609], [132.62, -12.86], [136.004, -10.235], [136.373, -7.589], [135.368, -3.343], [132.599, -1.969], [129.912, -3.159]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 16, "ty": "sh", "ix": 17, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[141.696, 0], [144.67, 0], [144.67, -12.716], [148.629, 0], [151.254, 0], [155.295, -12.716], [155.295, 0], [158.453, 0], [158.453, -14.829], [153.408, -14.829], [150.024, -4.041], [146.885, -14.829], [141.696, -14.829]], "c": true}, "ix": 2}, "nm": "m", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 2.5, "op": 25, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "02092020", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-105, 15, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [60, 60, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "02092020002", "np": 3, "mn": "ADBE Checkbox Control", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Checkbox", "mn": "ADBE Checkbox Control-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}]}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 3, "nm": "Color & Stroke Change", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2, "l": 2, "x": "var $bm_rt;\n$bm_rt = effect('Axis')('Point');"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar temp;\ntemp = effect('Scale')('Slider');\n$bm_rt = [\n    temp,\n    temp\n];"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Stroke", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 70, "ix": 1}}]}, {"ty": 5, "nm": "Axis", "np": 3, "mn": "ADBE Point Control", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "Point", "mn": "ADBE Point Control-0001", "ix": 1, "v": {"a": 0, "k": [250, 250], "ix": 1}}]}, {"ty": 5, "nm": "Scale", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1}}]}, {"ty": 5, "nm": "Primary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 1}}]}, {"ty": 5, "nm": "Secondary", "np": 3, "mn": "ADBE Color Control", "ix": 5, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 1}}]}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "outline 15", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-16.91, -18.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 64, "s": [-32.91, -34.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 88, "s": [303.09, 302.838, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [-440.91, -445.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [-16.91, -18.162, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 64, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-142.502, -143.044], [61.273, 61.273]], "c": false}]}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-258.106, -258.44], [61.273, 61.273]], "c": false}]}, {"t": 89, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-142.502, -143.044], [61.273, 61.273]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156881094, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 339, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 30, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [254]}, {"t": 180, "s": [3944]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "outline 17", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [132.66, -27.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 70, "s": [124.66, -34.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [352.66, 198.44, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [-348.34, -505.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [132.66, -27.56, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.111, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-131.095, -131.99]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-168.288, -169.297]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-187.471, -188.114]], "c": false}]}, {"i": {"x": 0.09, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-329.538, -329.547]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-131.095, -131.99]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588235294, 0.074509803922, 0.192156877705, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 178, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 26, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [-55]}, {"t": 180, "s": [-3524]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "outline 16", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [132.66, -27.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 70, "s": [124.66, -34.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [352.66, 198.44, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [-348.34, -505.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [132.66, -27.56, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[76.368, 77.217], [-19.737, -20.348]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588235294, 0.074509803922, 0.192156877705, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 54, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 100, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [-81]}, {"t": 180, "s": [-3624]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "outline 19", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-27.34, 130.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 73, "s": [-35.34, 123.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 91, "s": [192.66, 356.44, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [-508.34, -347.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [-27.34, 130.44, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.111, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-131.095, -131.99]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-168.288, -169.297]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-187.471, -188.114]], "c": false}]}, {"i": {"x": 0.09, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-329.538, -329.547]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-85.709, -85.206], [-131.095, -131.99]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588235294, 0.074509803922, 0.192156877705, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 178, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 26, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [-97]}, {"t": 180, "s": [-3566]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "outline 18", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-27.34, 130.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 73, "s": [-35.34, 123.44, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 91, "s": [192.66, 356.44, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [-508.34, -347.56, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [-27.34, 130.44, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[76.368, 77.217], [-19.737, -20.348]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588235294, 0.074509803922, 0.192156877705, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 54, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 100, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [-81]}, {"t": 180, "s": [-3624]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "outline 21", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-43.91, -124.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 80, "s": [-59.91, -140.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 94, "s": [276.09, 196.838, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [-467.91, -551.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [-43.91, -124.162, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 89, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 95, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"t": 106, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156881094, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 100, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 160, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [54]}, {"t": 180, "s": [-3586]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 89, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 95, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"t": 106, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 100, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 160, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [184]}, {"t": 180, "s": [-3456]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "outline 20", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-133.91, -56.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 76, "s": [-149.91, -72.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 93, "s": [186.09, 264.838, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [-557.91, -483.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [-133.91, -56.162, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 69, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 85, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 94, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"t": 105, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156881094, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 100, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 160, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [10]}, {"t": 180, "s": [-3630]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 69, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 85, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 94, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[222.537, 223.653], [-29.185, -30.005]], "c": false}]}, {"t": 105, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[112.505, 112.685], [-29.185, -30.005]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 100, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 160, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.411], "y": [1]}, "o": {"x": [0.452], "y": [0]}, "t": 0, "s": [140]}, {"t": 180, "s": [-3500]}], "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Star-1", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 48, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 68, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 74, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 76, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 80, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 82, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 84, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 86, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 88, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 92, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 94, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 96, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 98, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 100, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 104, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 106, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 108, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 110, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 112, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 114, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 116, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 122, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 124, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 126, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 128, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 130, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 132, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 134, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 136, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 138, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 140, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 142, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 144, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 146, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 148, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 150, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 152, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 156, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 158, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 160, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 163, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 166, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 169, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 172, "s": [1]}, {"t": 180, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-79.718, 77.116, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 73, "s": [-87.718, 70.116, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 91, "s": [140.282, 303.116, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [-560.718, -400.884, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [-79.718, 77.116, 0]}], "ix": 2, "l": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [0, 0, 0], "to": [0.167, 0.667, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 2, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 34, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 54, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 78, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 84, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 86, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 88, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 90, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 92, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 94, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 98, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 102, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 106, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 108, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 110, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 112, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 114, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 116, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 118, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 122, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 124, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 126, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 128, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 130, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 134, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 138, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 142, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 144, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 146, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 148, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 150, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.5, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 152, "s": [1, 4, 0], "to": [-0.5, -0.333, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 154, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 156, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 158, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 160, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [1, 4, 0], "to": [-0.167, 0, 0], "ti": [-0.167, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 166, "s": [-1, 0, 0], "to": [0.167, -0.333, 0], "ti": [-0.333, -0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 169, "s": [2, 2, 0], "to": [0.333, 0.667, 0], "ti": [0.333, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 172, "s": [1, 4, 0], "to": [-0.333, -0.333, 0], "ti": [0.167, 0.667, 0]}, {"t": 180, "s": [0, 0, 0]}], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[12.243, -12.243], [34.63, 0], [12.243, 12.243], [0, 34.63], [-12.243, 12.243], [-34.63, 0], [-12.243, -12.243], [0, -34.63]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Star-2", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 48, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 68, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 74, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 76, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 80, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 82, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 84, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 86, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 88, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 92, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 94, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 96, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 98, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 100, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 104, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 106, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 108, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 110, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 112, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 114, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 116, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 122, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 124, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 126, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 128, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 130, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 132, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 134, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 136, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 138, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 140, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 142, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 144, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 146, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 148, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 150, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 152, "s": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 156, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 158, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 160, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 163, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 166, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 169, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 172, "s": [-1]}, {"t": 180, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [79.66, -82.06, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 70, "s": [71.66, -89.06, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [299.66, 143.94, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [-401.34, -560.06, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [79.66, -82.06, 0]}], "ix": 2, "l": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [0, 0, 0], "to": [-0.333, -0.5, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 2, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 34, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 54, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 78, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 84, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 86, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 88, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 90, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 92, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 94, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 98, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 102, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 106, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 108, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 110, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 112, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 114, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 116, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 118, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 122, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 124, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 126, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 128, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 130, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 134, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 138, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 142, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 144, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 146, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 148, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 150, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.833, -0.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 152, "s": [-2, -3, 0], "to": [0.833, 0.5, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 154, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 156, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 158, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 160, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [-0.5, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [-2, -3, 0], "to": [0.5, 0.167, 0], "ti": [0, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 166, "s": [3, 1, 0], "to": [0, 0.167, 0], "ti": [0.833, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 169, "s": [-2, -2, 0], "to": [-0.833, -0.667, 0], "ti": [-0.333, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 172, "s": [-2, -3, 0], "to": [0.333, 0.333, 0], "ti": [-0.333, -0.5, 0]}, {"t": 180, "s": [0, 0, 0]}], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[12.243, -12.243], [34.63, 0], [12.243, 12.243], [0, 34.63], [-12.243, 12.243], [-34.63, 0], [-12.243, -12.243], [0, -34.63]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.658823549747, 0.541176497936, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Star-3", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 48, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 58, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 64, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 68, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 74, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 76, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 80, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 82, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 84, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 86, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 88, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 92, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 94, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 96, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 98, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 100, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 104, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 106, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 108, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 110, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 112, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 114, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 116, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 122, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 124, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 126, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 128, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 130, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 132, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 134, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 136, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 138, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 140, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 142, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 144, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 146, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 148, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 150, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 152, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 154, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 156, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 158, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 160, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 163, "s": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 166, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 169, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 172, "s": [1]}, {"t": 180, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.483, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [90.09, 92.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.9, "y": 0}, "t": 64, "s": [74.09, 76.838, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 88, "s": [410.09, 413.838, 0], "h": 1}, {"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [-333.91, -334.162, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [90.09, 92.838, 0]}], "ix": 2, "l": 2}, "a": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [0, 0, 0], "to": [0.333, -0.5, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 2, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 22, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 30, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 34, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 36, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 46, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 48, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 54, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 58, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 68, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 78, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 80, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 84, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 86, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 88, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 90, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 92, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 94, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 98, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 102, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 104, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 106, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 108, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 110, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 112, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 114, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 116, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 118, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 122, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 124, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 126, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 128, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 130, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 134, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 138, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 142, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 144, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 146, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 148, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 150, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.5, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 152, "s": [2, -3, 0], "to": [-0.5, 0.333, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 154, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 156, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 158, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 160, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0.167, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [2, -3, 0], "to": [-0.167, 0.167, 0], "ti": [0, -0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 166, "s": [-1, 1, 0], "to": [0, 0.333, 0], "ti": [-0.5, 0.667, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 169, "s": [2, -1, 0], "to": [0.5, -0.667, 0], "ti": [0.333, -0.167, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 172, "s": [2, -3, 0], "to": [-0.333, 0.167, 0], "ti": [0.333, -0.5, 0]}, {"t": 180, "s": [0, 0, 0]}], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, -66.609], [21.643, -22.756], [70.037, -15.724], [35.018, 18.41], [43.285, 66.609], [0, 43.853], [-43.285, 66.609], [-35.018, 18.41], [-70.037, -15.724], [-21.643, -22.756]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 70), comp('1865-shooting-stars-outline').layer('Color & Stroke Change').effect('Stroke')('Slider'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}], "markers": []}