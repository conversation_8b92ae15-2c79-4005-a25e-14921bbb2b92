import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import AOS from "aos";
import "aos/dist/aos.css";
import pinia from "./store";

import BootstrapVueNext from "bootstrap-vue-next";
// import vClickOutside from 'click-outside-vue3'
// import VueApexCharts from 'vue3-apexcharts'
import { vMaska } from "maska";

import VueFeat<PERSON> from "vue-feather";

import "@/assets/scss/config/default/app.scss";
// import "leaflet/dist/leaflet.css";
import "@/assets/scss/mermaid.min.css";
import "bootstrap/dist/js/bootstrap.bundle";

// i18n 相關套件
import { createI18n } from "vue-i18n";

import zh_TW from "@/locales/zh-TW/zh-tw.json";
import zh_CN from "@/locales/zh-CN/zh-cn.json";
import en_US from "@/locales/en/en.json";
import ja_JPN from "@/locales/ja/ja.json";

const messages = {
  zh_TW: zh_TW,
  zh_CN: zh_CN,
  en_US: en_US,
  ja_JPN: ja_JPN,
};

const i18n = createI18n({
  legacy: false,
  locale: localStorage.getItem("locale") || "en_US", // 預設語系英文
  fallbackLocale: localStorage.getItem("locale") || "en_US",
  messages,
});

// Toast
import Toast from "vue-toastification";
import "vue-toastification/dist/index.css";

// Loading
import VueLoading from "vue-loading-overlay";
import "vue-loading-overlay/dist/css/index.css";

// API Config
import getConfig from "./apis";

// Passive Event Listeners
import "default-passive-events";

import { debounce, throttle } from "@/utils/debounce";

// Element Plus
// import ElementPlus from 'element-plus'
// import 'element-plus/dist/index.css'

// Vue Date Picker
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'

AOS.init({
  easing: "ease-out-back",
  duration: 1000,
});

const app = createApp(App);

app.directive("debounce", {
  mounted(el, binding) {
    const { value, arg } = binding;
    const debouncedFn = debounce(value, arg);
    el.addEventListener("click", debouncedFn);
  },
  beforeUnmount(el, binding) {
    const { value } = binding;
    el.removeEventListener("click", value);
  },
});

app.directive("throttle", {
  mounted(el, binding) {
    const { value, arg } = binding;
    const throttledFn = throttle(value, arg);
    el.addEventListener("click", throttledFn);
  },
  beforeUnmount(el, binding) {
    const { value } = binding;
    el.removeEventListener("click", value);
  },
});

app.use(pinia);
app.use(router);
app.use(getConfig);
// app.use(VueApexCharts)
app.use(BootstrapVueNext);
app.use(i18n);
app.use(Toast, {
  transition: "Vue-Toastification__bounce",
  maxToasts: 20,
  newestOnTop: true,
});
// app.use(ElementPlus)
app.component(VueFeather.type, VueFeather);
app.component('VueDatePicker', VueDatePicker);
app.component("Loading", VueLoading);
app.directive("maska", vMaska);
// .use(vClickOutside)
app.mount("#app");
