# Tabulator 資料堆疊問題修復

## 🔍 **問題分析**

### **問題現象**：
當從 **Loadport 模式** 切換到 **IPC 模式** 時，table 的資料會一直堆疊，而不是更新原本的資訊。

### **根本原因**：
Tabulator 的 `updateOrAddData` 方法依賴於 `index` 字段來識別和更新現有的資料行，但在模式切換時：

1. **Loadport 模式**：`index = 'port_id'`
   ```javascript
   const indexField = props.mode === 'Loadport' ? 'port_id' : 'device_id';
   ```

2. **IPC 模式**：`index = 'device_id'`

3. **問題流程**：
   - Loadport 資料：`{port_id: "DEV_001_LP1", device_id: "DEV_001", ...}`
   - 切換到 IPC 模式後，index 變為 `device_id`
   - 新的 IPC 資料：`{device_id: "DEV_001", ...}` (沒有 port_id)
   - `updateOrAddData` 無法匹配現有行，因為舊資料的索引是 `port_id`
   - 結果：新資料被當作新行添加，導致資料堆疊

## 🛠️ **解決方案**

### **核心修復**：
在模式切換時強制使用 `setData` 而不是 `updateOrAddData`

```javascript
// 追蹤當前模式，用於檢測模式切換
let currentMode = ref(props.mode);

const updateTableData = (newData) => {
  // 檢測模式是否改變
  const modeChanged = currentMode.value !== props.mode;
  if (modeChanged) {
    console.log('Mode changed from', currentMode.value, 'to', props.mode, '- forcing setData');
    currentMode.value = props.mode;
  }

  // 如果是初始載入、模式改變，或資料為空，使用 setData
  if (currentDataLength === 0 || modeChanged) {
    console.log('Using setData for:', modeChanged ? 'mode change' : 'initial load');
    tableTabulator.value.setData(newData);  // 完全替換資料
  } else {
    console.log('Using updateOrAddData for incremental update');
    tableTabulator.value.updateOrAddData(newData);  // 增量更新
  }
};
```

### **修復邏輯**：
1. **追蹤模式變化**：使用 `currentMode` 變數記錄當前模式
2. **檢測模式切換**：比較 `currentMode.value` 與 `props.mode`
3. **強制完全更新**：模式切換時使用 `setData` 完全替換資料
4. **保持增量更新**：同模式下的資料更新仍使用 `updateOrAddData`

## 🎯 **修復效果**

### **修復前**：
```
Loadport 模式：
Row 1: {port_id: "DEV_001_LP1", device_id: "DEV_001", ...}
Row 2: {port_id: "DEV_001_LP2", device_id: "DEV_001", ...}

切換到 IPC 模式後：
Row 1: {port_id: "DEV_001_LP1", device_id: "DEV_001", ...}  ← 舊資料
Row 2: {port_id: "DEV_001_LP2", device_id: "DEV_001", ...}  ← 舊資料
Row 3: {device_id: "DEV_001", ...}                          ← 新資料 (堆疊)
Row 4: {device_id: "DEV_002", ...}                          ← 新資料 (堆疊)
```

### **修復後**：
```
Loadport 模式：
Row 1: {port_id: "DEV_001_LP1", device_id: "DEV_001", ...}
Row 2: {port_id: "DEV_001_LP2", device_id: "DEV_001", ...}

切換到 IPC 模式後：
Row 1: {device_id: "DEV_001", ...}  ← 完全替換
Row 2: {device_id: "DEV_002", ...}  ← 完全替換
```

## 🧪 **測試場景**

### **場景 1：模式切換**
1. 在 Loadport 模式下載入資料
2. 切換到 IPC 模式
3. **預期**：資料完全替換，不會堆疊

### **場景 2：同模式更新**
1. 在 IPC 模式下載入資料
2. 保持 IPC 模式，資料更新
3. **預期**：使用增量更新，保持滾動位置

### **場景 3：多次切換**
1. Loadport → IPC → Loadport → IPC
2. **預期**：每次切換都完全替換資料

## 📋 **日誌輸出**

修復後的日誌會顯示：
```
updateTableData called with: 5 items
Current mode: IPC Previous mode: Loadport
Mode changed from Loadport to IPC - forcing setData
Using setData for: mode change
```

## 🎉 **關鍵改進**

1. **✅ 解決資料堆疊**：模式切換時強制完全替換
2. **✅ 保持性能**：同模式下仍使用增量更新
3. **✅ 詳細日誌**：便於調試和監控
4. **✅ 向後兼容**：不影響現有功能

## 🔄 **進一步修復 - 解決每秒更新的堆疊問題**

### **發現的深層問題**：
用戶反映修復後仍然有堆疊問題，因為：
1. **每秒更新**：`setInterval` 每秒調用 `getIpcStatus`
2. **模式切換後**：`currentMode` 只在第一次檢測到變化，後續更新不再觸發 `setData`
3. **索引字段不匹配**：Tabulator 的 `index` 配置沒有更新，導致 `updateOrAddData` 持續失敗

### **根本問題**：
```javascript
// 問題：模式切換時只更改了列定義，沒有更新 index 配置
watch(() => props.mode, (newMode) => {
  tableTabulator.value.setColumns(columnDefinitions[newMode])  // ❌ 只更改列
  // index 仍然是舊的，導致 updateOrAddData 無法匹配資料
});
```

### **完整修復方案**：

#### **1. 增強資料結構檢測**
```javascript
// 檢查資料結構是否與當前索引字段匹配
const dataStructureMatches = newData && newData.length > 0 &&
                             Object.prototype.hasOwnProperty.call(newData[0], currentIndexField.value);

// 使用 setData 的條件擴展：
// 1. 初始載入
// 2. 模式改變
// 3. 索引字段改變
// 4. 資料結構不匹配當前索引字段 ← 新增
if (currentDataLength === 0 || modeChanged || indexFieldChanged || !dataStructureMatches) {
  tableTabulator.value.setData(newData);
}
```

#### **2. 模式切換時重新初始化 Tabulator**
```javascript
watch(() => props.mode, (newMode) => {
  stopInterval()
  console.log('Switched to mode tab:', newMode);

  // 重新初始化 Tabulator，更新 index 配置
  tableTabulatorFunc();  // ✅ 完全重建，包含新的 index 字段

  refresh(newMode)
  startInterval();
});
```

### **修復邏輯流程**：
1. **模式切換** → 重新初始化 Tabulator (新的 index 字段)
2. **每秒更新** → 檢查資料結構是否匹配當前 index
3. **匹配** → 使用 `updateOrAddData` 增量更新
4. **不匹配** → 使用 `setData` 完全替換

### **最終效果**：
- **✅ 模式切換**：完全重建 Tabulator，確保 index 字段正確
- **✅ 每秒更新**：智能檢測資料結構，選擇正確的更新方法
- **✅ 防止堆疊**：資料結構不匹配時強制使用 `setData`
- **✅ 保持性能**：同模式下仍使用增量更新

這個修復確保了在模式切換時資料能正確更新，而不是堆疊在一起，同時解決了每秒更新導致的持續堆疊問題。
