<script setup>
import { ref, nextTick, provide } from "vue";
import { RouterView } from 'vue-router';



const isRouterAlive = ref(true);

const reload = () => {
  isRouterAlive.value = false
  nextTick(() => {
    isRouterAlive.value = true
  })
}

provide('reload', reload)


</script>

<template>
  <router-view v-slot="{ Component }" v-if="isRouterAlive">
      <transition name="fade" mode="out-in">
        <component :is="Component"></component>
      </transition>
    </router-view>
</template>

<style>
@import "tabulator-tables";
@import "@/assets/style/table.css";


.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}


.tabulator .tabulator-header .tabulator-col .tabulator-col-content .tabulator-col-sorter {
  display: none !important;
}

.tabulator .tabulator-header .tabulator-headers .tabulator-col {
    border-right-width: 1px !important;
    border-color: #e2e8f0 !important;
}

.tabulator .tabulator-row .tabulator-cell {
    border-right-width: 1px !important;
    border-bottom: 1px solid !important;
    border-color: #e2e8f0 !important;
    overflow: visible !important;
}

.tabulator .tabulator-row.tabulator-row-even .tabulator-cell {
    border-color: #c9d3e0 !important;
}

.dark .tabulator .tabulator-header .tabulator-headers .tabulator-col {
    border-right-width: 1px !important;
    border-color: #3e3e3e !important;
}

.dark .tabulator .tabulator-row .tabulator-cell {
    border-right-width: 1px !important;
    border-color: #3e3e3e !important;
    overflow: visible !important;
}

.dark .tabulator .tabulator-row.tabulator-row-even .tabulator-cell {
    border-color: #555 !important;
}

.fs-c-10 {
  font-size: 10rem !important;
}

</style>
